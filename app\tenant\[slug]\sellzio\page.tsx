"use client"

import React, { useState, useEffect, use } from 'react'
import { useRouter } from 'next/navigation'
import { Sell<PERSON>Header } from '@/components/themes/sellzio/sellzio-header'
import { SellzioFacet } from '@/components/themes/sellzio/sellzio-facet'
import SellzioCategories from '@/components/themes/sellzio/categories'
import ShopeeLiveVideo from '@/components/themes/sellzio/shopee-live-video'
import { SellzioCartModal, type CartItem } from '@/components/themes/sellzio/cart'
import { SellzioSearchSuggestions } from '@/components/themes/sellzio/search/sellzio-search-suggestions'
import { SellzioSearchPredictions } from '@/components/themes/sellzio/search/sellzio-search-predictions'
import { useCart } from '@/hooks/use-cart'
import '@/components/themes/sellzio/sellzio-styles.css'
import Head from 'next/head'
// Import data dari file terpisah
import { type Product as ProductType } from '@/components/data/products'
import { keywordPredictionDB } from '@/components/data/keywords'
// Import komponen masonry dan product card Sellzio
import { SellzioMasonryLayout } from '@/components/themes/sellzio/masonry/sellzio-masonry-layout'
import { SellzioProductCard } from '@/components/themes/sellzio/product-card/sellzio-product-card'
import { useTenantTheme } from '@/components/tenant/tenant-theme-provider'
import { useTenantProducts } from '@/hooks/use-tenant-products'

// Type definitions
interface Prediction {
  text: string
  type: string
  relevance: number
}



interface TenantSellzioPageProps {
  params: Promise<{ slug: string }>
}

const TenantSellzioPage = ({ params }: TenantSellzioPageProps) => {
  const router = useRouter()
  const { theme, loading: themeLoading } = useTenantTheme()

  // Unwrap params using React.use()
  const resolvedParams = use(params)
  const tenantSlug = resolvedParams.slug

  // Fetch products from database
  const { products: dbProducts, loading: productsLoading, error: productsError } = useTenantProducts(tenantSlug)

  console.log('🔥 TENANT PAGE: Database products:', dbProducts.length, 'Loading:', productsLoading, 'Error:', productsError)
  console.log('🔥 TENANT PAGE: Database products data:', dbProducts)

  // Use database products - hook already handles fallback properly
  const products = dbProducts

  console.log('🔥 TENANT PAGE: Using products:', products.length, 'from database for tenant:', tenantSlug)
  console.log('🔥 TENANT PAGE: Flash sale test products:', products.filter(p => p.name.includes('Flash Sale Test')).map(p => ({ name: p.name, discount: p.discount, flash_sale: (p as any).flash_sale })))

  // Test Supabase connection directly
  useEffect(() => {
    const testSupabase = async () => {
      try {
        console.log('🔥 DIRECT TEST: Testing Supabase connection...')
        const { getClient } = await import('@/lib/supabase')
        const supabase = getClient()
        const { data, error } = await supabase
          .from('products')
          .select('id, name, tenant_id')
          .eq('tenant_id', tenantSlug)
          .limit(3)

        console.log('🔥 DIRECT TEST: Supabase result:', { data, error, tenantSlug })
      } catch (err) {
        console.error('🔥 DIRECT TEST: Supabase error:', err)
      }
    }

    testSupabase()
  }, [tenantSlug])

  // Convert products to ProductType format for compatibility with existing components
  const compatibleProducts: ProductType[] = products.map(product => ({
    id: product.id,
    name: product.name,
    shortName: product.shortName || product.name,
    category: product.category,
    subcategory: product.subcategory,
    price: product.price,
    originalPrice: product.originalPrice || product.price,
    discount: product.discount || '0%',
    rating: product.rating || '0',
    sold: product.sold || '0 terjual',
    shipping: product.shipping || 'Gratis Ongkir',
    image: product.image,
    isMall: product.isMall || false,
    cod: product.cod || false,
    storeName: 'Default Store', // Required by ProductType
    storeLocation: product.address?.city || 'Jakarta',
    address: product.address, // PERBAIKAN: Tambahkan field address untuk filter provinsi/kota
    isTerlaris: (product as any).isTerlaris || false,
    isLive: (product as any).isLive || false
  }))

  // Debug: Log products with address data
  console.log('🏠 TENANT DEBUG: Products with address:', compatibleProducts.slice(0, 3).map(p => ({
    id: p.id,
    name: p.name,
    address: p.address
  })))

  const [searchValue, setSearchValue] = useState('')
  const [isSearchExpanded, setIsSearchExpanded] = useState(false)
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [showMoreSuggestions, setShowMoreSuggestions] = useState(false)
  const [searchHistory, setSearchHistory] = useState<string[]>([])
  const [showPredictions, setShowPredictions] = useState(false)
  const [predictions, setPredictions] = useState<Prediction[]>([])
  const [searchResults, setSearchResults] = useState<ProductType[]>([])
  const [originalSearchResults, setOriginalSearchResults] = useState<ProductType[]>([]) // Store original results for filtering
  const [isSearchResultShown, setIsSearchResultShown] = useState(false)
  const [hideMainContent, setHideMainContent] = useState(false)
  const [userInteractionHistory, setUserInteractionHistory] = useState<string[]>([])
  const [searchFrequency, setSearchFrequency] = useState<{ [key: string]: number }>({})
  const [showFilterTabs, setShowFilterTabs] = useState(false)
  const [activeFilterTab, setActiveFilterTab] = useState('terkait')
  const [priceSortDirection, setPriceSortDirection] = useState<'asc' | 'desc'>('asc')
  const [showFilterIcon, setShowFilterIcon] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [showFacetPanel, setShowFacetPanel] = useState(false)
  const [facetFilters, setFacetFilters] = useState<{[key: string]: string[]}>({})
  const [filterBadgeCount, setFilterBadgeCount] = useState(0)
  const [subcategoryContext, setSubcategoryContext] = useState<{
    category: string
    selectedSubcategory: string
    allSubcategories: any[]
  } | null>(null)

  // State untuk suggestions popup
  const [showSuggestionsPopup, setShowSuggestionsPopup] = useState(false)

  // State untuk successful search history - riwayat pencarian yang berhasil menemukan produk
  const [successfulSearchHistory, setSuccessfulSearchHistory] = useState<string[]>([])

  // State untuk cart
  const [isCartModalOpen, setIsCartModalOpen] = useState(false)
  const { cartItems, cartCount } = useCart()

  // Convert SimpleCartItem to CartItem for modal compatibility
  const convertedCartItems: CartItem[] = cartItems.map(item => ({
    id: item.id,
    productId: item.id,
    name: item.name,
    price: item.price,
    originalPrice: item.price,
    image: item.image,
    store: "Default Store",
    storeId: "store-1",
    quantity: item.quantity,
    variant: "",
    selected: false,
    isLive: false,
    badges: []
  }))

  // Function to load user interaction history from localStorage
  const loadUserInteractionHistory = () => {
    try {
      const history = localStorage.getItem('keywordPredictionHistory')
      if (history) {
        const parsedHistory = JSON.parse(history)
        setUserInteractionHistory(parsedHistory)
        console.log('Loaded prediction history:', parsedHistory)
      } else {
        console.log('No prediction history found in localStorage')
      }
    } catch (e) {
      console.log('Failed to load prediction history from localStorage', e)
    }
  }

  // Function to load search frequency from localStorage
  const loadSearchFrequency = () => {
    try {
      const savedFrequency = localStorage.getItem('searchFrequency')
      if (savedFrequency) {
        setSearchFrequency(JSON.parse(savedFrequency))
      }
    } catch (e) {
      console.log('Failed to load search frequency from localStorage', e)
    }
  }

  // Function to update search frequency
  const updateSearchFrequency = (keyword: string) => {
    setSearchFrequency(prev => {
      const updated = { ...prev }
      updated[keyword] = (updated[keyword] || 0) + 1
      localStorage.setItem('searchFrequency', JSON.stringify(updated))
      return updated
    })
  }

  // Function to save successful search to history - hanya pencarian yang menemukan produk
  const saveSuccessfulSearch = (keyword: string) => {
    if (!keyword || keyword.trim() === '') return

    const normalizedKeyword = keyword.trim().toLowerCase()

    setSuccessfulSearchHistory(prev => {
      // Hapus keyword jika sudah ada (untuk memindahkan ke posisi teratas)
      const filtered = prev.filter(item => item.toLowerCase() !== normalizedKeyword)

      // Tambahkan ke posisi teratas
      const updated = [keyword.trim(), ...filtered].slice(0, 20) // Maksimal 20 item

      // Simpan ke localStorage
      localStorage.setItem('successfulSearchHistory', JSON.stringify(updated))

      return updated
    })
  }

  // Function to load successful search history from localStorage
  const loadSuccessfulSearchHistory = () => {
    try {
      const history = localStorage.getItem('successfulSearchHistory')
      if (history) {
        const parsedHistory = JSON.parse(history)
        setSuccessfulSearchHistory(parsedHistory)
        console.log('Loaded successful search history:', parsedHistory)
      }
    } catch (e) {
      console.log('Failed to load successful search history from localStorage', e)
    }
  }

  // Function to convert text to Title Case (huruf besar di depan setiap kata)
  const toTitleCase = (text: string) => {
    return text.toLowerCase()
  }

  // Function to get trending keywords based on search frequency
  const getTrendingKeywords = () => {
    // Default trending keywords if no search frequency data
    const defaultTrending = [
      'tas sekolah', 'tas selempang', 'handphone', 'tas mata', 'tas'
    ]

    // If no search frequency data, return default
    if (Object.keys(searchFrequency).length === 0) {
      return defaultTrending
    }

    // Sort keywords by frequency and get top 5
    const sortedKeywords = Object.entries(searchFrequency)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([keyword]) => keyword)

    // If we have less than 5, fill with defaults
    while (sortedKeywords.length < 5) {
      const nextDefault = defaultTrending[sortedKeywords.length]
      if (nextDefault && !sortedKeywords.includes(nextDefault)) {
        sortedKeywords.push(nextDefault)
      } else {
        break
      }
    }

    return sortedKeywords
  }

  // Function to get popular products based on search frequency
  const getPopularProducts = () => {
    // Default popular products
    const defaultProducts = [
      { name: 'Samsung Galaxy', image: 'https://images.unsplash.com/photo-*************-5f897ff02aa9?w=150&h=150&fit=crop' },
      { name: 'Sneakers Pria', image: 'https://images.unsplash.com/photo-**********-b41d501d3772?w=150&h=150&fit=crop' },
      { name: 'Tas Selempang', image: 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop' },
      { name: 'Headphone Bluetooth', image: 'https://images.unsplash.com/photo-*************-5e560c06d30e?w=150&h=150&fit=crop' },
      { name: 'Keyboard Gaming', image: 'https://images.unsplash.com/photo-*************-b024d705b90a?w=150&h=150&fit=crop' },
      { name: 'Power Bank', image: 'https://images.unsplash.com/photo-*************-b43bada2f4b8?w=150&h=150&fit=crop' },
      { name: 'Smart TV', image: 'https://images.unsplash.com/photo-*************-a4bb92f829d1?w=150&h=150&fit=crop' },
      { name: 'Tas Selempang', image: 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop' }
    ]

    // If no search frequency data, return default
    if (Object.keys(searchFrequency).length === 0) {
      return defaultProducts
    }

    // Map search frequency to products
    const productMapping: { [key: string]: { name: string, image: string } } = {
      'samsung galaxy': { name: 'Samsung Galaxy', image: 'https://images.unsplash.com/photo-*************-5f897ff02aa9?w=150&h=150&fit=crop' },
      'smartphone': { name: 'Samsung Galaxy', image: 'https://images.unsplash.com/photo-*************-5f897ff02aa9?w=150&h=150&fit=crop' },
      'sneakers': { name: 'Sneakers Pria', image: 'https://images.unsplash.com/photo-**********-b41d501d3772?w=150&h=150&fit=crop' },
      'sepatu': { name: 'Sneakers Pria', image: 'https://images.unsplash.com/photo-**********-b41d501d3772?w=150&h=150&fit=crop' },
      'tas selempang': { name: 'Tas Selempang', image: 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop' },
      'tas': { name: 'Tas Selempang', image: 'https://images.unsplash.com/photo-**********-98eeb64c6a62?w=150&h=150&fit=crop' },
      'headphone': { name: 'Headphone Bluetooth', image: 'https://images.unsplash.com/photo-*************-5e560c06d30e?w=150&h=150&fit=crop' },
      'keyboard': { name: 'Keyboard Gaming', image: 'https://images.unsplash.com/photo-*************-b024d705b90a?w=150&h=150&fit=crop' },
      'power bank': { name: 'Power Bank', image: 'https://images.unsplash.com/photo-*************-b43bada2f4b8?w=150&h=150&fit=crop' },
      'smart tv': { name: 'Smart TV', image: 'https://images.unsplash.com/photo-*************-a4bb92f829d1?w=150&h=150&fit=crop' }
    }

    // Get products based on search frequency
    const popularProducts: { name: string, image: string }[] = []
    const sortedKeywords = Object.entries(searchFrequency)
      .sort(([, a], [, b]) => b - a)

    for (const [keyword] of sortedKeywords) {
      const lowerKeyword = keyword.toLowerCase()
      for (const [productKey, product] of Object.entries(productMapping)) {
        if (lowerKeyword.includes(productKey) && !popularProducts.some(p => p.name === product.name)) {
          popularProducts.push(product)
          if (popularProducts.length >= 8) break
        }
      }
      if (popularProducts.length >= 8) break
    }

    // Fill with defaults if needed
    while (popularProducts.length < 8) {
      const nextDefault = defaultProducts[popularProducts.length]
      if (nextDefault && !popularProducts.some(p => p.name === nextDefault.name)) {
        popularProducts.push(nextDefault)
      } else {
        break
      }
    }

    return popularProducts
  }

  // Function to generate step-by-step predictions based on input
  const generatePredictions = (input: string) => {
    if (!input || input.trim() === '') {
      setPredictions([])
      return
    }

    const inputLower = input.toLowerCase().trim()
    const inputWords = inputLower.split(' ').filter(word => word.length > 0)
    const inputWordCount = inputWords.length

    console.log('🔍 TENANT PREDICTION: Input:', inputLower)
    console.log('🔍 TENANT PREDICTION: Input words:', inputWords)
    console.log('🔍 TENANT PREDICTION: Input word count:', inputWordCount)

    // Menyimpan hasil berdasarkan kategori
    let historyResults: any[] = []
    let productResults: any[] = []
    let relatedResults: any[] = []
    let synonymResults: any[] = []
    let correctionResults: any[] = []
    let trendingResults: any[] = []

    // Helper function untuk mencocokkan kata step-by-step
    const matchesStepByStep = (keyword: string, inputWords: string[]): boolean => {
      const keywordWords = keyword.toLowerCase().split(' ').filter(word => word.length > 0)
      const inputWordCount = inputWords.length

      // Keyword harus memiliki jumlah kata yang sama atau lebih dari input
      if (keywordWords.length < inputWordCount) {
        return false
      }

      // Cocokkan kata-kata lengkap (kecuali kata terakhir)
      for (let i = 0; i < inputWordCount - 1; i++) {
        if (keywordWords[i] !== inputWords[i]) {
          return false
        }
      }

      // Cocokkan kata terakhir (partial match)
      const lastInputWord = inputWords[inputWordCount - 1]
      const lastKeywordWord = keywordWords[inputWordCount - 1]

      return lastKeywordWord.startsWith(lastInputWord)
    }

    // Function to calculate relevance score
    const calculateRelevance = (prediction: string, input: string, type: string) => {
      let score = 0

      // Bobot berdasarkan tipe
      const typeWeights = {
        'history': 100,
        'trending': 85,
        'product': 80,
        'correction': 75,
        'related': 60,
        'synonym': 50,
        'suggestion': 40
      }

      // Tambahkan bobot tipe
      score += typeWeights[type as keyof typeof typeWeights] || 0

      // Cari kata-kata dalam input
      const inputWords = input.toLowerCase().split(' ')
      const predictionWords = prediction.toLowerCase().split(' ')
      const predictionLower = prediction.toLowerCase()

      // Jika prediksi dimulai dengan input, tambahkan skor lebih tinggi
      if (predictionLower.startsWith(input.toLowerCase())) {
        score += 30
      }

      // Jika prediksi berisi input persis, tambahkan skor
      if (predictionLower.includes(input.toLowerCase())) {
        score += 20
      }

      // Bobot lebih tinggi untuk setiap kata dalam input yang ada dalam prediksi
      let matchingWords = 0
      inputWords.forEach(word => {
        if (word.length > 0) {
          const hasMatch = predictionWords.some(pw => pw.includes(word))
          if (hasMatch) {
            matchingWords++
            score += word.length * 2
          }
        }
      })

      // Tambahkan skor berdasarkan persentase kata yang cocok
      score += (matchingWords / inputWords.length) * 25

      return score
    }

    // 1. Tambahkan dari riwayat pencarian sukses (maksimal 4)
    let historyCount = 0
    console.log('🔍 TENANT: Checking search history:', successfulSearchHistory)

    for (let i = 0; i < successfulSearchHistory.length && historyCount < 4; i++) {
      const item: string = successfulSearchHistory[i]
      if (item && matchesStepByStep(item, inputWords)) {
        historyResults.push({
          text: item,
          type: 'history',
          relevance: calculateRelevance(item, inputLower, 'history')
        })
        historyCount++
        console.log('🔍 TENANT: Added history prediction:', item)
      }
    }

    // 2. Tambahkan dari keyword produk - step-by-step matching
    console.log('🔍 TENANT: Checking product keywords...')
    try {
      if (keywordPredictionDB && (keywordPredictionDB as any).productKeywords) {
        (keywordPredictionDB as any).productKeywords.forEach((keyword: string) => {
          const matches = matchesStepByStep(keyword, inputWords)
          if (matches) {
            console.log(`🔍 TENANT: Product match found: "${keyword}" for input "${inputLower}"`)
            productResults.push({
              text: keyword,
              type: 'product',
              relevance: calculateRelevance(keyword, inputLower, 'product')
            })
          }
        })
      }
    } catch (e) {
      // Fallback keywords if database fails
      const fallbackKeywords = ['smartphone', 'laptop', 'tas', 'sepatu', 'baju', 'sepatu sekolah', 'sepatu sekolah wanita']
      fallbackKeywords.forEach(keyword => {
        const matches = matchesStepByStep(keyword, inputWords)
        if (matches) {
          productResults.push({
            text: keyword,
            type: 'product',
            relevance: calculateRelevance(keyword, inputLower, 'product')
          })
        }
      })
    }

    // 3. Tambahkan trending keywords (maksimal 5) - step-by-step matching
    const trendingKeywords = getTrendingKeywords()
    console.log('🔍 TENANT: Trending keywords:', trendingKeywords)
    trendingKeywords.forEach((keyword, index) => {
      const matches = matchesStepByStep(keyword, inputWords)
      if (matches) {
        console.log(`🔍 TENANT: Trending match found: "${keyword}" for input "${inputLower}"`)
        const baseRelevance = calculateRelevance(keyword, inputLower, 'trending')
        const positionBonus = (5 - index) * 5 // 25, 20, 15, 10, 5 poin tambahan
        trendingResults.push({
          text: keyword,
          type: 'trending',
          relevance: baseRelevance + positionBonus
        })
      }
    })

    // 4. Tambahkan dari related keywords - hanya untuk kata lengkap
    try {
      if (keywordPredictionDB && (keywordPredictionDB as any).relatedKeywords) {
        inputWords.forEach((word, index) => {
          // Hanya proses kata yang sudah lengkap (bukan kata terakhir yang sedang diketik)
          if (index < inputWords.length - 1) {
            const relatedKeywords = (keywordPredictionDB as any).relatedKeywords[word]
            if (relatedKeywords) {
              relatedKeywords.forEach((related: string) => {
                // Buat kombinasi dengan kata-kata sebelumnya dan related keyword
                const combinedWords = [...inputWords.slice(0, index), related, ...inputWords.slice(index + 1)]
                const combinedText = combinedWords.join(' ')

                if (matchesStepByStep(combinedText, inputWords)) {
                  relatedResults.push({
                    text: combinedText,
                    type: 'related',
                    relevance: calculateRelevance(combinedText, inputLower, 'related')
                  })
                }
              })
            }
          }
        })
      }
    } catch (e) {
      console.log('🔍 TENANT: Related keywords not available')
    }

    // 5. Tambahkan dari sinonim - hanya untuk kata lengkap
    try {
      if (keywordPredictionDB && (keywordPredictionDB as any).synonyms) {
        inputWords.forEach((word, index) => {
          // Hanya proses kata yang sudah lengkap (bukan kata terakhir yang sedang diketik)
          if (index < inputWords.length - 1) {
            const synonyms = (keywordPredictionDB as any).synonyms[word]
            if (synonyms) {
              synonyms.forEach((synonym: string) => {
                // Buat kombinasi dengan sinonim
                const combinedWords = [...inputWords]
                combinedWords[index] = synonym
                const combinedText = combinedWords.join(' ')

                if (matchesStepByStep(combinedText, inputWords)) {
                  synonymResults.push({
                    text: combinedText,
                    type: 'synonym',
                    relevance: calculateRelevance(combinedText, inputLower, 'synonym')
                  })
                }
              })
            }
          }
        })
      }
    } catch (e) {
      console.log('🔍 TENANT: Synonyms not available')
    }

    // 6. Periksa kemungkinan typo - hanya untuk kata lengkap
    try {
      if (keywordPredictionDB && (keywordPredictionDB as any).typoCorrections) {
        inputWords.forEach((word, index) => {
          // Hanya proses kata yang sudah lengkap (bukan kata terakhir yang sedang diketik)
          if (index < inputWords.length - 1) {
            const correction = (keywordPredictionDB as any).typoCorrections[word]
            if (correction) {
              // Buat kombinasi dengan koreksi typo
              const combinedWords = [...inputWords]
              combinedWords[index] = correction
              const combinedText = combinedWords.join(' ')

              if (matchesStepByStep(combinedText, inputWords)) {
                correctionResults.push({
                  text: combinedText,
                  type: 'correction',
                  relevance: calculateRelevance(combinedText, inputLower, 'correction')
                })
              }
            }
          }
        })
      }
    } catch (e) {
      console.log('🔍 TENANT: Typo corrections not available')
    }

    // Gabungkan semua hasil
    const allResults = [
      ...historyResults,
      ...productResults,
      ...trendingResults,
      ...relatedResults,
      ...synonymResults,
      ...correctionResults
    ]

    // Urutkan berdasarkan relevansi
    allResults.sort((a, b) => b.relevance - a.relevance)

    // Hapus duplikat berdasarkan text (tapi pertahankan yang relevance tertinggi)
    const uniqueResults: any[] = []
    const seenTexts = new Set()

    allResults.forEach(result => {
      if (!seenTexts.has(result.text.toLowerCase())) {
        seenTexts.add(result.text.toLowerCase())
        uniqueResults.push(result)
      }
    })

    // Batasi prediksi antara 4-12 item
    const limitedPredictions = uniqueResults.slice(0, Math.max(4, Math.min(12, uniqueResults.length)))

    console.log('🔍 TENANT: Final predictions:', limitedPredictions)
    setPredictions(limitedPredictions)
  }

  // Function to handle search input change
  const handleSearchChange = (value: string) => {
    setSearchValue(value)

    // Show predictions when typing (minimal 1 character) and search is expanded
    if (value.trim().length >= 1 && isSearchExpanded) {
      setShowPredictions(true)
      setShowSuggestions(false) // Hide suggestions when predictions are shown
      setIsSearchResultShown(false) // Reset search results
      setSearchResults([])
      generatePredictions(value)
      document.body.classList.add('show-suggestions') // Use same class for overlay effect
    } else if (value.trim() === '' && isSearchExpanded) {
      // Show suggestions when input is empty
      setShowPredictions(false)
      setShowSuggestions(true)
      setIsSearchResultShown(false) // Reset search results
      setSearchResults([])
      document.body.classList.add('show-suggestions')
    } else {
      setShowPredictions(false)
      setShowSuggestions(false)
      setIsSearchResultShown(false)
      setSearchResults([])
      document.body.classList.remove('show-suggestions')
    }
  }

  // Function to handle prediction click - sama dengan halaman utama
  const handlePredictionClick = (prediction: Prediction) => {
    setSearchValue(prediction.text)
    setShowPredictions(false)
    setShowSuggestions(false)

    // Update search frequency for trending keywords
    updateSearchFrequency(prediction.text)

    // Add to user interaction history - sesuai docs/facet.html
    // Hindari duplikat dengan menghapus item yang sama
    const predictionText: string = prediction.text
    const newHistory = [...userInteractionHistory]
    const index = newHistory.indexOf(predictionText)
    if (index !== -1) {
      newHistory.splice(index, 1)
    }

    // Tambahkan ke awal array
    newHistory.unshift(predictionText)

    // Batasi jumlah item riwayat ke 20 sesuai docs/facet.html
    if (newHistory.length > 20) {
      newHistory.pop()
    }

    // Update state
    setUserInteractionHistory(newHistory)

    // Simpan ke localStorage jika tersedia
    try {
      localStorage.setItem('keywordPredictionHistory', JSON.stringify(newHistory))
      console.log('🔍 TENANT: Saved prediction history to localStorage:', newHistory)
    } catch (e) {
      console.log('🔍 TENANT: Failed to save prediction history to localStorage', e)
    }

    // Tambahkan ke search history juga - PERBAIKAN UTAMA
    if (!searchHistory.includes(predictionText)) {
      const newSearchHistory = [predictionText, ...searchHistory.slice(0, 11)] // Maksimal 12 item
      setSearchHistory(newSearchHistory)
      localStorage.setItem('searchHistory', JSON.stringify(newSearchHistory))
      console.log('🔍 TENANT: Added to search history:', predictionText)
    }

    // Jalankan pencarian otomatis - sesuai docs/facet.html
    executeSearch(prediction.text)

    console.log('🔍 TENANT: Prediction clicked and search executed:', prediction.text)
  }

  // Function to execute search
  const executeSearch = (searchText: string) => {
    if (!searchText || searchText.trim() === '') return

    const query = searchText.trim()
    const results = enhancedSearch(query)

    setOriginalSearchResults(results)
    setSearchResults(results)
    setIsSearchResultShown(true)
    setHideMainContent(true)
    setShowPredictions(false)
    setShowSuggestions(false)
    setShowFilterTabs(true)
    setShowFilterIcon(true)

    // Save to search history if results found
    if (results.length > 0) {
      saveSuccessfulSearch(query)
      setSearchHistory(prev => {
        const filtered = prev.filter(item => item.toLowerCase() !== query.toLowerCase())
        const updated = [query, ...filtered].slice(0, 20)
        localStorage.setItem('searchHistory', JSON.stringify(updated))
        return updated
      })
    }

    // Update body classes
    document.body.classList.remove('show-suggestions')
    document.body.classList.add('hide-main-content')
  }

  // Enhanced search function
  const enhancedSearch = (query: string): ProductType[] => {
    if (!query || query.trim() === '') return []

    const searchTerms = query.toLowerCase().trim().split(' ')
    const results: (ProductType & { searchScore: number, matchDetails: string[] })[] = []

    compatibleProducts.forEach(product => {
      let score = 0
      const matchDetails: string[] = []

      // Exact name match (highest score)
      if (product.name.toLowerCase() === query.toLowerCase()) {
        score += 100
        matchDetails.push('exact_name')
      }

      // Name contains query
      if (product.name.toLowerCase().includes(query.toLowerCase())) {
        score += 50
        matchDetails.push('name_contains')
      }

      // Category match
      if (product.category.toLowerCase().includes(query.toLowerCase())) {
        score += 30
        matchDetails.push('category')
      }

      // Subcategory match
      if (product.subcategory && product.subcategory.toLowerCase().includes(query.toLowerCase())) {
        score += 25
        matchDetails.push('subcategory')
      }

      // Individual word matches
      searchTerms.forEach(term => {
        if (term.length >= 2) {
          if (product.name.toLowerCase().includes(term)) {
            score += 10
            matchDetails.push(`word_${term}`)
          }
        }
      })

      // Add to results if score > 0
      if (score > 0) {
        results.push({
          ...product,
          searchScore: score,
          matchDetails
        })
      }
    })

    // Sort by score (highest first)
    return results
      .sort((a, b) => b.searchScore - a.searchScore)
      .slice(0, 50) // Limit results
  }



  // Function to handle suggestion click - sama dengan halaman utama
  const handleSuggestionClick = (suggestion: string) => {
    setSearchValue(suggestion)

    // Update search frequency for trending keywords
    updateSearchFrequency(suggestion)

    // Add to user interaction history untuk prediksi keyword
    const newUserHistory = [...userInteractionHistory]
    const existingIndex = newUserHistory.indexOf(suggestion)
    if (existingIndex !== -1) {
      // Hapus yang lama dan pindahkan ke depan
      newUserHistory.splice(existingIndex, 1)
    }
    newUserHistory.unshift(suggestion) // Tambahkan ke awal array

    // Batasi maksimal 20 item
    if (newUserHistory.length > 20) {
      newUserHistory.pop()
    }

    setUserInteractionHistory(newUserHistory)
    localStorage.setItem('keywordPredictionHistory', JSON.stringify(newUserHistory))
    console.log('🔍 TENANT: Added to user interaction history:', suggestion)

    // Tambahkan ke search history
    if (!searchHistory.includes(suggestion)) {
      const newSearchHistory = [suggestion, ...searchHistory.slice(0, 11)] // Maksimal 12 item
      setSearchHistory(newSearchHistory)
      localStorage.setItem('searchHistory', JSON.stringify(newSearchHistory))
    }

    // Auto search functionality - execute search immediately
    executeSearch(suggestion)

    console.log('🔍 TENANT: Suggestion clicked and auto search executed:', suggestion)
  }

  // Function to clear search history
  const clearSearchHistory = () => {
    setSearchHistory([])
    setSuccessfulSearchHistory([])
    localStorage.removeItem('searchHistory')
    localStorage.removeItem('successfulSearchHistory')
  }

  // Function to handle filter tab click
  const handleFilterTabClick = (filterType: string) => {
    setActiveFilterTab(filterType)

    if (filterType === 'harga') {
      // Toggle price sort direction
      setPriceSortDirection(prev => prev === 'asc' ? 'desc' : 'asc')
    }

    // Apply sorting to current search results
    if (searchResults.length > 0) {
      const sortedResults = sortSearchResults(searchResults, filterType)
      setSearchResults(sortedResults)
    }
  }

  // Function to sort search results based on filter type
  const sortSearchResults = (results: ProductType[], filterType: string): ProductType[] => {
    const sortedResults = [...results]

    switch (filterType) {
      case 'terkait':
        // Sort by relevance score (default)
        return sortedResults.sort((a, b) => ((b as any).searchScore || 0) - ((a as any).searchScore || 0))

      case 'terlaris':
        // Sort by sold count
        return sortedResults.sort((a, b) => {
          const aSold = parseInt(a.sold.replace(/\D/g, '')) || 0
          const bSold = parseInt(b.sold.replace(/\D/g, '')) || 0
          return bSold - aSold
        })

      case 'terbaru':
        // Sort by product ID (assuming higher ID = newer)
        return sortedResults.sort((a, b) => b.id - a.id)

      case 'harga':
        // Sort by price
        return sortedResults.sort((a, b) => {
          const aPrice = parseInt(a.price.replace(/\D/g, '')) || 0
          const bPrice = parseInt(b.price.replace(/\D/g, '')) || 0
          return priceSortDirection === 'asc' ? aPrice - bPrice : bPrice - aPrice
        })

      default:
        return sortedResults
    }
  }

  // Function to apply filters to search results
  const applyFilters = (results: ProductType[], filters: {[key: string]: string[]}) => {
    console.log('🎯 TENANT FILTER: Starting applyFilters with:', {
      resultsCount: results.length,
      filters
    });

    const filteredResults = results.filter(product => {
      // Filter berdasarkan kategori
      if (filters.kategori && filters.kategori.length > 0) {
        const matchesCategory = filters.kategori.some(filterCategory => {
          const filterCat = filterCategory.toLowerCase();
          const productCategory = product.category?.toLowerCase() || '';
          const productSubcategory = product.subcategory?.toLowerCase() || '';

          return productCategory === filterCat || productSubcategory === filterCat;
        });

        if (!matchesCategory) {
          return false;
        }
      }

      // Filter berdasarkan rentang harga
      if (filters['rentang harga'] && filters['rentang harga'].length > 0) {
        const price = parseInt(product.price.replace(/\D/g, ''))
        const inRange = filters['rentang harga'].some((range: string) => {
          if (range === "Di bawah Rp 100.000") {
            return price < 100000;
          } else if (range === "Rp 100.000 - Rp 500.000") {
            return price >= 100000 && price < 500000;
          } else if (range === "Rp 500.000 - Rp 1.000.000") {
            return price >= 500000 && price < 1000000;
          } else if (range === "Rp 1.000.000 - Rp 5.000.000") {
            return price >= 1000000 && price < 5000000;
          } else if (range === "Di atas Rp 5.000.000") {
            return price >= 5000000;
          }
          return false;
        })

        if (!inRange) {
          return false;
        }
      }

      // Filter berdasarkan rating
      if (filters.rating && filters.rating.length > 0) {
        const productRating = typeof product.rating === 'string' ? parseFloat(product.rating) : product.rating
        const meetsRating = filters.rating.some((rating: string) => {
          if (rating === "5 Bintang") {
            return productRating >= 5;
          } else if (rating === "4+ Bintang") {
            return productRating >= 4;
          } else if (rating === "3+ Bintang") {
            return productRating >= 3;
          }
          return false;
        })

        if (!meetsRating) {
          return false;
        }
      }

      // Filter berdasarkan pengiriman
      if (filters.pengiriman && filters.pengiriman.length > 0) {
        const matchesShipping = filters.pengiriman.some((shipping: string) => {
          return product.shipping === shipping;
        })

        if (!matchesShipping) {
          return false;
        }
      }

      // Filter berdasarkan fitur
      if (filters.fitur && filters.fitur.length > 0) {
        const matchesFeature = filters.fitur.some((feature: string) => {
          if (feature === "COD") {
            return product.cod;
          } else if (feature === "SellZio Mall") {
            return product.isMall;
          }
          return false;
        })

        if (!matchesFeature) {
          return false;
        }
      }

      return true;
    })

    console.log('🎯 TENANT FILTER: Filtered results:', filteredResults.length, 'products');
    return filteredResults;
  }

  // Check screen size for mobile detection
  useEffect(() => {
    const checkScreenSize = () => {
      setIsMobile(window.innerWidth < 1024)
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)

    return () => window.removeEventListener('resize', checkScreenSize)
  }, [])

  // Load data from localStorage on component mount
  useEffect(() => {
    loadUserInteractionHistory()
    loadSearchFrequency()
    loadSuccessfulSearchHistory()

    // Load search history
    try {
      const savedHistory = localStorage.getItem('searchHistory')
      if (savedHistory) {
        setSearchHistory(JSON.parse(savedHistory))
      }
    } catch (e) {
      console.log('Failed to load search history from localStorage', e)
    }
  }, [])

  // Event listener untuk subcategory search - sama seperti di halaman sellzio utama
  useEffect(() => {
    const handleSubcategorySearch = (event: CustomEvent) => {
      const { query, category, selectedSubcategory, allSubcategories, products: subcategoryProducts } = event.detail;

      console.log('🔥 TENANT PAGE: Received subcategory search event:', {
        query,
        category,
        selectedSubcategory,
        productsCount: subcategoryProducts?.length || 0
      });

      // Set subcategory context
      setSubcategoryContext({
        category,
        selectedSubcategory,
        allSubcategories
      });

      // Update search value dan execute search dengan produk yang sudah difilter
      setSearchValue(query);

      // Langsung set hasil pencarian tanpa perlu search ulang
      if (subcategoryProducts && subcategoryProducts.length > 0) {
        console.log('🔥 TENANT PAGE: Setting filtered products directly:', subcategoryProducts.length);
        // Kita bisa langsung menggunakan subcategoryProducts yang sudah difilter
        // Tapi untuk konsistensi, kita tetap panggil executeSearch
        executeSearch(query);
      } else {
        console.log('🔥 TENANT PAGE: No products found, executing normal search');
        executeSearch(query);
      }
    };

    // Add event listener
    if (typeof window !== 'undefined') {
      window.addEventListener('subcategorySearch', handleSubcategorySearch as EventListener);

      // Cleanup
      return () => {
        window.removeEventListener('subcategorySearch', handleSubcategorySearch as EventListener);
      };
    }
  }, [executeSearch]);

  // Theme loading
  if (themeLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading {tenantSlug} marketplace...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>{tenantSlug} - Sellzio Marketplace</title>
        <meta name="description" content={`Marketplace ${tenantSlug} powered by Sellzio`} />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
      </Head>

      <div className="sellzio-page">
        {/* Header */}
        <SellzioHeader
          searchValue={searchValue}
          onSearchChange={handleSearchChange}
          onSearchFocus={() => {
            setIsSearchExpanded(true)
            if (searchValue.trim() === '') {
              setShowSuggestions(true)
              setShowPredictions(false)
              document.body.classList.add('show-suggestions')
            }
          }}
          onSearchBlur={() => {}}
          onSearchExecute={executeSearch}
          isExpanded={isSearchExpanded}
          onToggleExpanded={() => setIsSearchExpanded(!isSearchExpanded)}
          onCartClick={() => setIsCartModalOpen(true)}
          cartCount={cartCount}
          onFilterClick={() => setShowFacetPanel(!showFacetPanel)}
          showFilterIcon={showFilterIcon && isMobile}
          filterBadgeCount={filterBadgeCount}
        />

        {/* Main Content */}
        <main className="main-content pt-20 container mx-auto py-0 px-0">
          {/* Loading State */}
          {productsLoading && (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-orange-500"></div>
              <span className="ml-2 text-gray-600">Memuat produk...</span>
            </div>
          )}

          {/* Error State */}
          {productsError && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6 mx-4">
              <p className="text-red-600">Error: {productsError}</p>
              <p className="text-sm text-red-500 mt-1">Menggunakan data contoh sebagai fallback.</p>
            </div>
          )}

          {/* Categories Section - Visual spacing: 24px dari header */}
          <section className="visual-spacing-24">
            <SellzioCategories
              products={compatibleProducts}
            />
          </section>

          {/* Shopee Live Video Section - Visual spacing: 24px dari categories */}
          <section className="visual-spacing-24">
            <ShopeeLiveVideo />
          </section>

          {/* Products Section - Visual spacing: 24px dari live video */}
          {!productsLoading && !productsError && compatibleProducts.length > 0 && (
            <section className="visual-spacing-24 pb-6">
              <div className="sellzio-products-container">
                <SellzioMasonryLayout
                  columnCount={{ mobile: 2, tablet: 3, desktop: 4 }}
                  gap={{ mobile: 6, tablet: 10, desktop: 12 }}
                >
                {compatibleProducts.slice(0, 24).map((product, index) => {
                  const rating = product.rating ? parseFloat(product.rating) : 0
                  const sold = product.sold ? parseInt(product.sold.replace(/\D/g, '')) || 0 : 0

                  let badgeType: "mall" | "star" | "star-lite" | "termurah" | "terlaris" | "komisi-xtra" | "none" = "none"
                  
                  if (product.isMall) badgeType = "mall"
                  else if ((product as any).isTerlaris) badgeType = "terlaris"
                  else if (rating >= 4.5) badgeType = "star"
                  else if (rating >= 4.0) badgeType = "star-lite"

                  let cardType: "standard" | "video" | "flash-sale" = "standard"
                  if ((product as any).isLive) cardType = "video"
                  else if ((product.discount && parseFloat(product.discount) > 0) || (product as any).flash_sale) cardType = "flash-sale"

                  // Props tambahan untuk flash sale
                  const additionalProps: any = {}
                  if (cardType === "flash-sale") {
                    const endTime = new Date()

                    // Pengaturan flash sale berdasarkan nama produk untuk testing
                    if (product.name.includes("Flash Sale Test - Waktu Habis")) {
                      // Test case: Waktu sudah habis (1 menit yang lalu) - TETAP FLASH SALE CARD TAPI EXPIRED
                      endTime.setMinutes(endTime.getMinutes() - 1)
                      additionalProps.stockSold = 50
                      additionalProps.totalStock = 100
                      additionalProps.isTimeExpired = true
                      // Tetap flash sale card tapi dengan status expired
                    } else if (product.name.includes("Flash Sale Test - Stock Habis")) {
                      // Test case: Stock habis tapi waktu masih ada
                      endTime.setHours(endTime.getHours() + 2)
                      additionalProps.stockSold = 100
                      additionalProps.totalStock = 100
                      additionalProps.isStockSoldOut = true
                    } else if (product.name.includes("Flash Sale Test - Normal")) {
                      // Test case: Flash sale normal
                      endTime.setHours(endTime.getHours() + 2)
                      additionalProps.stockSold = 25
                      additionalProps.totalStock = 100
                    } else if (product.name.includes("Flash Sale Test - Coming Soon")) {
                      // Test case: Flash sale belum dimulai (akan dimulai dalam 1 jam)
                      const startTime = new Date()
                      startTime.setHours(startTime.getHours() + 1) // Dimulai 1 jam dari sekarang
                      endTime.setHours(endTime.getHours() + 5) // Berakhir 5 jam dari sekarang (4 jam durasi)
                      additionalProps.startTime = startTime
                      additionalProps.stockSold = 0
                      additionalProps.totalStock = 100
                      additionalProps.isComingSoon = true
                    } else {
                      // Flash sale default untuk produk lain
                      endTime.setHours(endTime.getHours() + 2) // 2 jam dari sekarang
                      additionalProps.stockSold = Math.floor(Math.random() * 80) + 10 // 10-90 terjual
                      additionalProps.totalStock = 100
                    }

                    additionalProps.endTime = endTime
                  }

                  return (
                    <SellzioProductCard
                      key={product.id}
                      id={product.id}
                      type={cardType}
                      name={product.name}
                      price={product.price}
                      originalPrice={product.originalPrice}
                      discount={product.discount}
                      image={product.image}
                      rating={rating}
                      sold={sold}
                      hasCod={(product as any).hasCod}
                      isMall={product.isMall}
                      isTerlaris={(product as any).isTerlaris}
                      isLive={(product as any).isLive}
                      shipping={product.shipping}
                      city={product.address?.city || 'Jakarta'}
                      badgeType={badgeType}
                      onClick={() => router.push(`/tenant/${tenantSlug}/sellzio/products/${product.id}`)}
                      onAddToCart={() => {}}
                      {...additionalProps}
                    />
                  )
                })}
              </SellzioMasonryLayout>
            </div>
          </section>
          )}
        </main>

        {/* Keyword Predictions Container - Muncul saat mengetik minimal 1 huruf */}
        {showPredictions && (
          <SellzioSearchPredictions
            predictions={predictions}
            searchValue={searchValue}
            onPredictionClick={handlePredictionClick}
          />
        )}

        {/* Suggestions Container - Persis seperti docs/facet.html */}
        {showSuggestions && (
          <SellzioSearchSuggestions
            searchHistory={searchHistory}
            showMoreSuggestions={showMoreSuggestions}
            onToggleMore={() => setShowMoreSuggestions(!showMoreSuggestions)}
            onSuggestionClick={handleSuggestionClick}
            onClearHistory={clearSearchHistory}
            trendingKeywords={getTrendingKeywords()}
            popularProducts={getPopularProducts()}
          />
        )}

        {/* Filter Tabs - positioned in header area like docs/facet.html */}
        {showFilterTabs && (
          <div className="filter-tabs-header">
            <div className="filter-tabs-inner">
              <button
                className={`filter-tab ${activeFilterTab === 'terkait' ? 'active' : ''}`}
                onClick={() => handleFilterTabClick('terkait')}
              >
                Terkait
              </button>
              <button
                className={`filter-tab ${activeFilterTab === 'terbaru' ? 'active' : ''}`}
                onClick={() => handleFilterTabClick('terbaru')}
              >
                Terbaru
              </button>
              <button
                className={`filter-tab ${activeFilterTab === 'terlaris' ? 'active' : ''}`}
                onClick={() => handleFilterTabClick('terlaris')}
              >
                Terlaris
              </button>
              <button
                className={`filter-tab filter-tab-price ${activeFilterTab === 'harga' ? 'active' : ''}`}
                onClick={() => handleFilterTabClick('harga')}
              >
                <span>Harga</span>
                <i className={`fa ${priceSortDirection === 'asc' ? 'fa-arrow-up' : 'fa-arrow-down'}`}></i>
              </button>
            </div>
          </div>
        )}

        {/* Search Results Container - sama seperti halaman utama */}
        {isSearchResultShown && (
          <>
            {searchResults.length === 0 ? (
              // Show "Tidak Ditemukan" layout
              <div className="not-found-full-layout">
                <div className="not-found-container">
                  <div className="not-found-icon">
                    <div className="search-document-icon">
                      <div className="document-base"></div>
                      <div className="document-fold"></div>
                      <div className="document-lines"></div>
                      <div className="magnifying-glass"></div>
                    </div>
                  </div>
                  <div className="not-found-title">Hasil tidak ditemukan</div>
                  <div className="not-found-message">Mohon coba kata kunci yang lain atau yang lebih umum.</div>
                  <div className="not-found-button-container">
                    <button className="not-found-button primary" onClick={() => {
                      // Clear search and return to suggestions
                      setSearchValue('')
                      setSearchResults([])
                      setIsSearchResultShown(false)
                      setHideMainContent(false)
                      setIsSearchExpanded(true)
                      setShowSuggestions(true)
                      setShowPredictions(false)
                      setShowFilterTabs(false)
                      setShowFilterIcon(false)

                      // Reset body classes
                      document.body.classList.remove('hide-main-content')
                      document.body.classList.add('show-suggestions')

                      // Focus to input
                      setTimeout(() => {
                        const searchInput = document.querySelector('.search-input-expanded') as HTMLInputElement
                        if (searchInput) {
                          searchInput.focus()
                        }
                      }, 100)
                    }}>
                      Coba kata kunci lain
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              // Show search results with sidebar layout
              <div className="search-results-layout">
                {/* Desktop Facet Sidebar - Only visible when search results found */}
                <div className="desktop-facet-sidebar">
                  <SellzioFacet
                    searchResults={originalSearchResults}
                    displayedProducts={searchResults}
                    activeFilters={facetFilters}
                    onFiltersChange={(filters) => {
                      setFacetFilters(filters as { [key: string]: string[] })
                      // Update filter badge count
                      const count = Object.values(filters).reduce((total, values) => total + (values?.length || 0), 0)
                      setFilterBadgeCount(count)
                      // Apply filters to search results
                      if (originalSearchResults.length > 0) {
                        const filteredResults = applyFilters(originalSearchResults, filters as { [key: string]: string[] })
                        setSearchResults(filteredResults)
                      }
                    }}
                    isVisible={true} // Always visible for desktop sidebar
                    onClose={() => {}} // No close functionality for desktop sidebar
                    isDesktopSidebar={true} // New prop to indicate this is desktop sidebar
                    allProducts={compatibleProducts}
                    subcategoryContext={subcategoryContext}
                  />
                </div>

                {/* Search Results Content */}
                <div className="search-results-container">
                  <div className="sellzio-search-products-container">
                    <SellzioMasonryLayout columnCount={{ mobile: 2, tablet: 3, desktop: 4 }} gap={{ mobile: 6, tablet: 10, desktop: 12 }}>
                      {searchResults.map((product, index) => {
                        const rating = product.rating ? parseFloat(product.rating.toString()) : 0
                        const sold = product.sold ? parseInt(product.sold.toString().replace(/\D/g, '')) || 0 : 0

                        let badgeType: "mall" | "star" | "star-lite" | "termurah" | "terlaris" | "komisi-xtra" | "none" = "none"

                        if (product.isMall) badgeType = "mall"
                        else if ((product as any).isTerlaris) badgeType = "terlaris"
                        else if (rating >= 4.5) badgeType = "star"
                        else if (rating >= 4.0) badgeType = "star-lite"

                        // Tentukan jenis card - flash sale untuk produk dengan discount atau flash_sale flag
                        let cardType: "standard" | "flash-sale" = "standard"
                        if ((product.discount && parseFloat(product.discount) > 0) || (product as any).flash_sale) {
                          cardType = "flash-sale"
                        }

                        // Props tambahan untuk flash sale
                        const additionalProps: any = {}
                        if (cardType === "flash-sale") {
                          const endTime = new Date()

                          // Pengaturan flash sale berdasarkan nama produk untuk testing
                          if (product.name.includes("Flash Sale Test - Waktu Habis")) {
                            // Test case: Waktu sudah habis (1 menit yang lalu) - TETAP FLASH SALE CARD TAPI EXPIRED
                            endTime.setMinutes(endTime.getMinutes() - 1)
                            additionalProps.stockSold = 50
                            additionalProps.totalStock = 100
                            additionalProps.isTimeExpired = true
                            // Tetap flash sale card tapi dengan status expired
                          } else if (product.name.includes("Flash Sale Test - Stock Habis")) {
                            // Test case: Stock habis tapi waktu masih ada
                            endTime.setHours(endTime.getHours() + 2)
                            additionalProps.stockSold = 100
                            additionalProps.totalStock = 100
                            additionalProps.isStockSoldOut = true
                          } else if (product.name.includes("Flash Sale Test - Normal")) {
                            // Test case: Flash sale normal
                            endTime.setHours(endTime.getHours() + 2)
                            additionalProps.stockSold = 25
                            additionalProps.totalStock = 100
                          } else if (product.name.includes("Flash Sale Test - Coming Soon")) {
                            // Test case: Flash sale belum dimulai (akan dimulai dalam 1 jam)
                            const startTime = new Date()
                            startTime.setHours(startTime.getHours() + 1) // Dimulai 1 jam dari sekarang
                            endTime.setHours(endTime.getHours() + 5) // Berakhir 5 jam dari sekarang (4 jam durasi)
                            additionalProps.startTime = startTime
                            additionalProps.stockSold = 0
                            additionalProps.totalStock = 100
                            additionalProps.isComingSoon = true
                          } else {
                            // Flash sale default untuk produk lain
                            endTime.setHours(endTime.getHours() + 2)
                            additionalProps.stockSold = Math.floor(Math.random() * 80) + 10
                            additionalProps.totalStock = 100
                          }

                          additionalProps.endTime = endTime
                        }

                        return (
                          <SellzioProductCard
                            key={product.id}
                            id={product.id}
                            type={cardType}
                            name={product.name}
                            price={product.price}
                            originalPrice={product.originalPrice}
                            discount={product.discount}
                            image={product.image}
                            rating={rating}
                            sold={sold}
                            hasCod={(product as any).hasCod}
                            isMall={product.isMall}
                            isTerlaris={(product as any).isTerlaris}
                            isLive={(product as any).isLive}
                            shipping={product.shipping}
                            city={product.address?.city || 'Jakarta'}
                            badgeType={badgeType}
                            onClick={() => router.push(`/tenant/${tenantSlug}/sellzio/products/${product.id}`)}
                            onAddToCart={() => {}}
                            {...additionalProps}
                          />
                        )
                      })}
                    </SellzioMasonryLayout>
                  </div>
                </div>
              </div>
            )}
          </>
        )}

        {/* Mobile/Tablet Facet Component - Only for mobile/tablet popup and when results exist */}
        {searchResults.length > 0 && (
          <SellzioFacet
            searchResults={originalSearchResults}
            displayedProducts={searchResults}
            activeFilters={facetFilters}
            onFiltersChange={(filters) => {
              setFacetFilters(filters as { [key: string]: string[] })
              // Update filter badge count
              const count = Object.values(filters).reduce((total, values) => total + (values?.length || 0), 0)
              setFilterBadgeCount(count)
              // Apply filters to search results
              if (originalSearchResults.length > 0) {
                const filteredResults = applyFilters(originalSearchResults, filters as { [key: string]: string[] })
                setSearchResults(filteredResults)
              }
            }}
            isVisible={showFacetPanel}
            onClose={() => setShowFacetPanel(false)}
            isDesktopSidebar={false}
            allProducts={compatibleProducts}
            subcategoryContext={subcategoryContext}
          />
        )}

        {/* Cart Modal */}
        <SellzioCartModal
          isOpen={isCartModalOpen}
          onClose={() => setIsCartModalOpen(false)}
          cartItems={convertedCartItems}
        />

        {/* CSS Styles for Search Results */}
        <style jsx>{`
          /* Filter Tabs Styles */
          .filter-tabs-header {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            z-index: 1002;
            background-color: white;
            border-bottom: 1px solid #f2f2f2;
            height: 45px;
            display: flex;
            align-items: center;
            overflow-x: visible;
            -webkit-overflow-scrolling: touch;
          }

          .filter-tabs-inner {
            display: inline-flex;
            padding: 0 10px;
            padding-left: 25px;
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
          }

          .filter-tab {
            padding: 12px 15px;
            font-size: 14px;
            color: #666;
            cursor: pointer;
            position: relative;
            transition: color 0.2s ease;
            background: none;
            border: none;
            white-space: nowrap;
            flex: 1;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .filter-tab.active {
            color: #ee4d2d;
            font-weight: 500;
          }

          .filter-tab.active:after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 15px;
            right: 15px;
            height: 2px;
            background-color: #ee4d2d;
          }

          .filter-tab-price {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 5px;
          }

          .filter-tab-price i {
            font-size: 12px;
            margin: 0;
          }

          /* Adjust main content when filter tabs are shown - SAMA dengan halaman utama */
          .search-results-container {
            margin-top: ${showFilterTabs ? '45px' : '0'};
            padding-top: 20px;
          }

          /* Adjust layout when filter tabs are shown - SAMA dengan halaman utama */
          .search-results-layout {
            margin-top: ${showFilterTabs ? '90px' : '50px'} !important;
          }

          .sellzio-search-products-container {
            padding: 0 16px;
            max-width: 1200px;
            margin: 0 auto;
          }

          @media (max-width: 768px) {
            .sellzio-search-products-container {
              padding: 0 4px;
            }
          }

          @media (max-width: 1024px) {
            .desktop-facet-sidebar {
              display: none;
            }

            .search-results-layout {
              padding-left: 4px;
              padding-right: 4px;
            }
          }

          @media (max-width: 768px) {
            .filter-tabs-header {
              display: flex;
              justify-content: center;
              padding: 0;
              width: 100%;
            }

            .filter-tabs-inner {
              max-width: 800px;
              width: 100%;
              display: flex;
              justify-content: space-between;
              padding: 0;
              margin: 0 auto;
            }

            .filter-tab {
              flex: 1;
              text-align: center;
              white-space: nowrap;
              padding: 12px 8px;
              font-size: 13px;
              display: flex;
              align-items: center;
              justify-content: center;
            }

            .filter-tab.filter-tab-price {
              display: flex;
              justify-content: center;
              align-items: center;
              gap: 4px;
            }

            .filter-tab-price i {
              margin: 0;
              font-size: 11px;
            }
          }

          /* Not Found Styles */
          .not-found-full-layout {
            position: fixed;
            top: 60px;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .not-found-container {
            text-align: center;
            padding: 40px 20px;
            max-width: 400px;
          }

          .not-found-icon {
            margin-bottom: 24px;
          }

          .search-document-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto;
            position: relative;
          }

          .document-base {
            width: 60px;
            height: 70px;
            background: #f5f5f5;
            border-radius: 4px;
            position: absolute;
            top: 5px;
            left: 10px;
            border: 2px solid #ddd;
          }

          .document-fold {
            width: 12px;
            height: 12px;
            background: #ddd;
            position: absolute;
            top: 5px;
            right: 8px;
            clip-path: polygon(0 0, 0 100%, 100% 100%);
          }

          .document-lines {
            position: absolute;
            top: 20px;
            left: 18px;
            right: 18px;
          }

          .document-lines::before,
          .document-lines::after {
            content: '';
            display: block;
            height: 2px;
            background: #ccc;
            margin-bottom: 6px;
          }

          .magnifying-glass {
            width: 24px;
            height: 24px;
            border: 3px solid #ee4d2d;
            border-radius: 50%;
            position: absolute;
            bottom: 0;
            right: 0;
          }

          .magnifying-glass::after {
            content: '';
            width: 8px;
            height: 3px;
            background: #ee4d2d;
            position: absolute;
            bottom: -6px;
            right: -2px;
            transform: rotate(45deg);
          }

          .not-found-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
          }

          .not-found-message {
            font-size: 14px;
            color: #666;
            margin-bottom: 24px;
            line-height: 1.5;
          }

          .not-found-button-container {
            display: flex;
            flex-direction: column;
            gap: 12px;
          }

          .not-found-button {
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            border: none;
          }

          .not-found-button.primary {
            background: #ee4d2d;
            color: white;
          }

          .not-found-button.primary:hover {
            background: #d73527;
          }

          .not-found-button.secondary {
            background: #f5f5f5;
            color: #666;
            border: 1px solid #ddd;
          }

          .not-found-button.secondary:hover {
            background: #ebebeb;
          }
        `}</style>
      </div>
    </>
  )
}

export default TenantSellzioPage
