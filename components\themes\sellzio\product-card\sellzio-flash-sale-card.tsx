"use client"

import React, { useState, useEffect } from "react"
import Image from "next/image"
import {
  SellzioMallBadge,
  SellzioStarBadge,
  SellzioStarLiteBadge,
  SellzioCodBadge,
  SellzioDiscountBadge,
  SellzioRatingStars,
  SellzioShippingBadge,
} from "./sellzio-badges"

export interface SellzioFlashSaleCardProps {
  id: number
  name: string
  price: string
  originalPrice: string
  discount: string
  image: string
  rating?: number
  sold?: number
  hasCod?: boolean
  isMall?: boolean
  badgeType?: "mall" | "star" | "star-lite" | "none"
  shipping?: string
  endTime?: Date // Flash sale end time
  stockSold?: number // Items sold in flash sale
  totalStock?: number // Total stock for flash sale
  onClick?: () => void
}

export const SellzioFlashSaleCard = ({
  id,
  name,
  price,
  originalPrice,
  discount,
  image,
  rating = 0,
  sold = 0,
  hasCod = false,
  isMall = false,
  badgeType = "none",
  shipping = "Pengiriman Instan",
  endTime,
  stockSold = 0,
  totalStock = 100,
  onClick
}: SellzioFlashSaleCardProps) => {
  // imageLoaded state dihapus karena tidak diperlukan
  const [timeLeft, setTimeLeft] = useState({
    hours: 0,
    minutes: 0,
    seconds: 0
  })

  // Calculate time remaining
  useEffect(() => {
    if (!endTime) {
      setTimeLeft({ hours: 0, minutes: 0, seconds: 0 })
      return
    }

    // Initial calculation
    const calculateTimeLeft = () => {
      const now = new Date().getTime()
      const distance = endTime.getTime() - now

      if (distance > 0) {
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
        const seconds = Math.floor((distance % (1000 * 60)) / 1000)

        return { hours, minutes, seconds }
      } else {
        return { hours: 0, minutes: 0, seconds: 0 }
      }
    }

    // Set initial time
    setTimeLeft(calculateTimeLeft())

    const timer = setInterval(() => {
      const newTimeLeft = calculateTimeLeft()
      setTimeLeft(newTimeLeft)

      // Stop timer when time is up
      if (newTimeLeft.hours === 0 && newTimeLeft.minutes === 0 && newTimeLeft.seconds === 0) {
        clearInterval(timer)
      }
    }, 1000)

    return () => clearInterval(timer)
  }, [endTime])

  const getShippingType = (): "instan" | "gratis" | "reguler" => {
    if (shipping?.toLowerCase().includes("instan")) return "instan"
    if (shipping?.toLowerCase().includes("gratis")) return "gratis"
    return "reguler"
  }

  const progressPercentage = Math.min((stockSold / totalStock) * 100, 100)

  return (
    <div className="sellzio-flash-sale-card" onClick={onClick}>
      {/* Flash Sale Header */}
      <div className="sellzio-flash-header">
        <div className="sellzio-flash-badge">
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
            <path fillRule="evenodd" d="M14.615 1.595a.75.75 0 01.359.852L12.982 9.75h7.268a.75.75 0 01.548 1.262l-10.5 11.25a.75.75 0 01-1.272-.71L10.018 14.25H2.75a.75.75 0 01-.548-1.262l10.5-11.25a.75.75 0 01.913-.143z" clipRule="evenodd" />
          </svg>
          FLASH SALE
        </div>
        {endTime && (
          <div className="sellzio-countdown">
            <span className="sellzio-time-unit">{String(timeLeft.hours).padStart(2, '0')}</span>
            <span className="sellzio-time-separator">:</span>
            <span className="sellzio-time-unit">{String(timeLeft.minutes).padStart(2, '0')}</span>
            <span className="sellzio-time-separator">:</span>
            <span className="sellzio-time-unit">{String(timeLeft.seconds).padStart(2, '0')}</span>
          </div>
        )}
      </div>
      {/* Image Container - langsung tanpa gap */}
      <div className="sellzio-image-container">
        {/* Badge diskon dengan icon flash sale di kanan atas - menggantikan COD */}
        <div className="sellzio-flash-discount-badge-right">
          <SellzioDiscountBadge discount={discount} isFlashSale={true} />
        </div>

        {/* Loading placeholder dihapus karena tidak diperlukan */}
        <Image
          src={image}
          alt={name}
          fill
          className="sellzio-product-image"
          // onLoad handler dihapus karena tidak diperlukan
          sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
        />
      </div>

      {/* Product Info */}
      <div className="sellzio-product-info">
        {/* Product Name with badges */}
        <h3 className="sellzio-product-name">
          {/* Render badges based on priority */}
          {badgeType === "mall" || isMall ? (
            <SellzioMallBadge />
          ) : badgeType === "star" ? (
            <SellzioStarBadge />
          ) : badgeType === "star-lite" ? (
            <SellzioStarLiteBadge />
          ) : null}
          {name}
        </h3>

        {/* Special badges tidak didukung di flash sale card */}

        {/* Rating di bawah special badges dengan COD badge */}
        <div className="sellzio-rating-container">
          <SellzioRatingStars rating={rating} />
          {hasCod && <SellzioCodBadge />}
        </div>

        {/* Price with flash sale styling */}
        <div className="sellzio-flash-price-container" data-long-price={price.length > 10}>
          <div className="sellzio-price-left">
            <div className="sellzio-flash-current-price">{price}</div>
            {originalPrice && discount && (
              <div className="sellzio-flash-discount-ticket">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-4 h-4">
                  <path fillRule="evenodd" d="M1.5 6.375c0-1.036.84-1.875 1.875-1.875h17.25c1.035 0 1.875.84 1.875 1.875v3.026a.75.75 0 01-.375.65 2.249 2.249 0 000 3.898.75.75 0 01.375.65v3.026c0 1.035-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 011.5 17.625v-3.026a.75.75 0 01.374-.65 2.249 2.249 0 000-3.898.75.75 0 01-.374-.65V6.375zm15-1.125a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0V6a.75.75 0 01.75-.75zm.75 4.5a.75.75 0 00-1.5 0v.75a.75.75 0 001.5 0v-.75zm-.75 3a.75.75 0 01.75.75v.75a.75.75 0 01-1.5 0V15a.75.75 0 01.75-.75zm.75 4.5a.75.75 0 00-1.5 0v.75a.75.75 0 001.5 0V18zM6 12a.75.75 0 01.75-.75H12a.75.75 0 010 1.5H6.75A.75.75 0 016 12zm.75 2.25a.75.75 0 000 1.5h3a.75.75 0 000-1.5h-3z" clipRule="evenodd" />
                  <path d="M9 12l2 2 4-4" stroke="white" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" fill="none"/>
                </svg>
              </div>
            )}
          </div>
          <div className="sellzio-sold-count">Terjual {sold}</div>
        </div>

        {/* Stock Progress Bar */}
        <div className="sellzio-stock-progress">
          <div className="sellzio-stock-info">
            <span className="sellzio-stock-sold">Terjual {stockSold}</span>
            <span className="sellzio-stock-total">/ {totalStock}</span>
          </div>
          <div className="sellzio-progress-bar">
            <div 
              className="sellzio-progress-fill"
              style={{ width: `${progressPercentage}%` }}
            ></div>
          </div>
        </div>

        {/* Shipping */}
        <SellzioShippingBadge type={getShippingType()} />
      </div>

      <style jsx>{`
        .sellzio-flash-sale-card {
          background-color: white;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          cursor: pointer;
          transition: all 0.2s ease;
          width: 100%;
          border: 2px solid #ff6b35;
          position: relative;
        }

        .sellzio-flash-sale-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 16px rgba(255, 107, 53, 0.3);
        }

        .sellzio-flash-header {
          background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
          color: white;
          padding: 8px 12px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          font-size: 10px;
          font-weight: bold;
          margin-bottom: 0; /* Hilangkan margin bawah */
        }

        .sellzio-flash-badge {
          display: flex;
          align-items: center;
          gap: 4px;
        }

        .sellzio-countdown {
          display: flex;
          align-items: center;
          gap: 2px;
          font-family: monospace;
        }

        .sellzio-time-unit {
          background-color: rgba(255, 255, 255, 0.2);
          padding: 2px 4px;
          border-radius: 2px;
          min-width: 20px;
          text-align: center;
        }

        .sellzio-time-separator {
          font-weight: bold;
        }

        .sellzio-image-container {
          position: relative;
          width: 100%;
          padding-top: 100%; /* 1:1 Aspect Ratio */
          overflow: hidden;
          background-color: #f9fafb;
          z-index: 1; /* Base z-index untuk container */
          margin-top: 0; /* Hilangkan margin atas */
          border-top: none; /* Hilangkan border atas jika ada */
        }

        .sellzio-flash-discount-badge-right {
          position: absolute;
          top: 8px;
          right: 8px;
          z-index: 3; /* Badge diskon di atas foto produk */
        }

        .sellzio-product-image {
          object-fit: contain;
          /* transition opacity dihapus untuk menghilangkan animasi */
          opacity: 1; /* Langsung tampil seperti card produk biasa */
          padding: 8px; /* Padding yang sama dengan product card biasa */
          z-index: 2; /* Z-index untuk foto produk */
          margin-top: 0; /* Hilangkan margin atas */
        }

        .sellzio-product-image.loaded {
          opacity: 1;
        }

        .sellzio-image-placeholder {
          position: absolute;
          inset: 0;
          background-color: #f3f4f6;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 1; /* Z-index untuk placeholder */
        }

        /* Badge diskon harus di atas foto produk */
        .sellzio-image-container > div[class*="absolute"] {
          z-index: 3 !important; /* Badge diskon di atas foto */
        }

        .sellzio-loading-shimmer {
          width: 60%;
          height: 60%;
          background: linear-gradient(
            90deg,
            #f3f4f6 25%,
            #e5e7eb 50%,
            #f3f4f6 75%
          );
          background-size: 200% 100%;
          animation: shimmer 1.5s infinite;
          border-radius: 4px;
        }

        @keyframes shimmer {
          0% {
            background-position: -200% 0;
          }
          100% {
            background-position: 200% 0;
          }
        }

        .sellzio-product-info {
          padding: 12px;
        }

        .sellzio-product-name {
          font-size: 13px;
          font-weight: 500;
          color: #374151;
          line-height: 1.4;
          margin-bottom: 8px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }

        /* Rating container dengan COD badge */
        .sellzio-rating-container {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 8px;
        }

        .sellzio-flash-price-container {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-top: 8px;
          margin-bottom: 8px;
        }

        .sellzio-price-left {
          display: flex;
          align-items: center;
          gap: 8px;
        }

        .sellzio-flash-current-price {
          font-size: 16px;
          font-weight: 700;
          color: #ff6b35;
        }

        .sellzio-flash-discount-ticket {
          color: #f97316;
          display: flex;
          align-items: center;
        }

        .sellzio-product-meta {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          font-size: 11px;
          color: #6b7280;
        }

        .sellzio-meta-divider {
          width: 1px;
          height: 12px;
          background-color: #d1d5db;
          margin: 0 8px;
        }

        .sellzio-sold-count {
          font-size: 11px;
          color: #6b7280;
        }

        .sellzio-stock-progress {
          margin-bottom: 8px;
        }

        .sellzio-stock-info {
          display: flex;
          justify-content: space-between;
          font-size: 10px;
          color: #6b7280;
          margin-bottom: 4px;
        }

        .sellzio-stock-sold {
          color: #ff6b35;
          font-weight: 600;
        }

        .sellzio-progress-bar {
          width: 100%;
          height: 4px;
          background-color: #f3f4f6;
          border-radius: 2px;
          overflow: hidden;
        }

        .sellzio-progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #ff6b35 0%, #f7931e 100%);
          border-radius: 2px;
          transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
          .sellzio-flash-sale-card {
            border-radius: 6px;
          }

          .sellzio-product-info {
            padding: 10px;
          }

          .sellzio-product-name {
            font-size: 12px;
          }

          .sellzio-flash-current-price {
            font-size: 14px;
            flex-shrink: 1;
            min-width: 80px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .sellzio-sold-count {
            font-size: 9px;
            flex-shrink: 0;
            white-space: nowrap;
          }

          .sellzio-flash-price-container {
            display: flex;
            align-items: center;
            justify-content: space-between;
            gap: 8px;
            flex-wrap: nowrap;
          }

          .sellzio-price-left {
            display: flex;
            align-items: center;
            gap: 4px;
            flex: 1;
            min-width: 0;
            overflow: hidden;
          }

          .sellzio-flash-discount-ticket {
            flex-shrink: 0;
          }

          /* Desktop/Tablet: Semua elemen wajib ada dan sejajar */
          @media (min-width: 769px) {
            .sellzio-flash-price-container {
              display: flex;
              align-items: center;
              justify-content: space-between;
              gap: 8px;
            }

            .sellzio-price-left {
              display: flex;
              align-items: center;
              gap: 8px;
            }

            .sellzio-flash-discount-ticket {
              display: flex !important;
            }

            .sellzio-sold-count {
              display: block !important;
            }
          }

          /* Mobile priority: harga panjang = tiket, harga pendek = terjual */
          @media (max-width: 768px) {
            .sellzio-flash-current-price {
              max-width: 110px;
            }

            /* Jika harga panjang (>10 karakter), sembunyikan terjual, tampilkan tiket */
            .sellzio-flash-price-container[data-long-price="true"] .sellzio-sold-count {
              display: none;
            }

            .sellzio-flash-price-container[data-long-price="true"] .sellzio-flash-discount-ticket {
              display: flex;
            }

            /* Jika harga pendek, sembunyikan tiket, tampilkan terjual */
            .sellzio-flash-price-container[data-long-price="false"] .sellzio-flash-discount-ticket {
              display: none;
            }

            /* Harga pendek wajib sejajar dengan terjual */
            .sellzio-flash-price-container[data-long-price="false"] {
              display: flex;
              align-items: center;
              justify-content: space-between;
              gap: 8px;
              flex-wrap: nowrap;
            }
          }

          .sellzio-flash-header {
            padding: 4px 8px;
            font-size: 9px;
          }
        }
      `}</style>
    </div>
  )
}
