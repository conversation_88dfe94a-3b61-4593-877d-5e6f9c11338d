@import url("https://fonts.googleapis.com/css2?family=Roboto:wght@400;500;700&display=swap");

/* Reset dan base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: '<PERSON><PERSON>', <PERSON><PERSON>, sans-serif;
}

html, body {
  width: 100%;
  overflow-x: hidden;
  margin: 0;
  padding: 0;
  position: relative;
}

/* Prevent layout shift during hydration */
html {
  scroll-behavior: smooth;
}

/* Ensure consistent rendering */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  background-color: #f9fafb !important; /* Konsisten dengan background halaman */
  color: #333;
  position: relative;
}

/* Pastikan body memiliki background yang konsisten */
body {
  background-color: #f9fafb !important; /* Warna background utama dengan !important untuk override */
}

/* Global header fix untuk semua halaman */
.header {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  z-index: 1000 !important;
  background-color: #ee4d2d !important;
  min-height: 60px !important;
  display: flex !important;
  align-items: center !important;
  box-sizing: border-box !important;
}

/* Header dan Navigasi */
.header {
  background-color: #ee4d2d;
  padding: 10px 15px;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1000; /* Single z-index value */
  transition: all 0.3s ease;
  box-sizing: border-box;
  min-height: 60px; /* Ensure consistent height */
}

/* Ensure header is always properly styled regardless of page context */
header.header,
.header {
  background-color: #ee4d2d !important;
  padding: 10px 15px !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  z-index: 1000 !important;
  transition: all 0.3s ease !important;
  box-sizing: border-box !important;
  min-height: 60px !important;
  display: block !important;
  opacity: 1 !important;
  visibility: visible !important;
}

.search-container {
  display: flex;
  align-items: center;
  position: relative;
  max-width: 800px;
  margin: 0 auto;
  width: 100%;
  box-sizing: border-box;
  overflow: visible;
  min-height: 40px; /* Ensure consistent height */
}

/* Ensure search container is always properly styled */
.header .search-container,
header .search-container {
  display: flex !important;
  align-items: center !important;
  position: relative !important;
  max-width: 800px !important;
  margin: 0 auto !important;
  width: 100% !important;
  box-sizing: border-box !important;
  overflow: visible !important;
  min-height: 40px !important;
}

.search-input-wrapper {
  position: relative;
  flex-grow: 1;
  margin-right: 85px;
}

/* Remove margin-right in expanded mode to allow full width */
.search-expanded .search-input-wrapper {
  margin-right: 0;
}

.search-input {
  width: 100%;
  padding: 10px 60px 10px 15px;
  border: 2px solid #ee4d2d;
  border-radius: 8px;
  font-size: 14px;
  outline: none;
  color: #333;
  background-color: #FFFFFF;
  box-shadow: 0 1px 3px rgba(0,0,0,0.1);
  position: relative;
  z-index: 10;
}

/* Ensure search input uses full width in expanded mode */
.search-expanded .search-input {
  width: 100%;
  padding-right: 60px; /* Space for clear and search icons */
}

/* Placeholder warna oranye */
.search-input::placeholder {
  color: #ee4d2d;
  opacity: 1;
}

/* Animated placeholder */
.search-placeholder {
  position: absolute;
  left: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #ee4d2d;
  pointer-events: none;
  font-size: 14px;
  transition: opacity 0.3s;
  display: flex;
  flex-direction: column;
  width: calc(100% - 60px);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  height: 20px;
  z-index: 11;
}

.placeholder-static {
  white-space: nowrap;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 1;
  animation: staticPlaceholderAnimation 12s infinite;
}

.placeholder-dynamic {
  position: relative;
  width: 100%;
  overflow: hidden;
  height: 20px;
  max-width: calc(100% - 60px);
  line-height: 1.2;
}

.placeholder-text {
  position: absolute;
  width: 100%;
  display: flex;
  align-items: center;
  opacity: 0;
  visibility: hidden;
  top: 0;
  left: 0;
  animation: placeholderAnimation 45s infinite;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

@keyframes staticPlaceholderAnimation {
  0%, 8% {
    opacity: 1;
  }
  12%, 96% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

@keyframes placeholderAnimation {
  0% {
    opacity: 0;
    transform: translateY(20px);
    visibility: hidden;
  }
  1%, 5% {
    opacity: 1;
    transform: translateY(0);
    visibility: visible;
  }
  6%, 100% {
    opacity: 0;
    transform: translateY(-20px);
    visibility: hidden;
  }
}

.placeholder-text:nth-child(2) { animation-delay: 3s; }
.placeholder-text:nth-child(3) { animation-delay: 6s; }
.placeholder-text:nth-child(4) { animation-delay: 9s; }
.placeholder-text:nth-child(5) { animation-delay: 12s; }
.placeholder-text:nth-child(6) { animation-delay: 15s; }
.placeholder-text:nth-child(7) { animation-delay: 18s; }
.placeholder-text:nth-child(8) { animation-delay: 21s; }
.placeholder-text:nth-child(9) { animation-delay: 24s; }
.placeholder-text:nth-child(10) { animation-delay: 27s; }
.placeholder-text:nth-child(11) { animation-delay: 30s; }
.placeholder-text:nth-child(12) { animation-delay: 33s; }
.placeholder-text:nth-child(13) { animation-delay: 36s; }
.placeholder-text:nth-child(14) { animation-delay: 39s; }
.placeholder-text:nth-child(15) { animation-delay: 42s; }

/* Sembunyikan placeholder ketika input focus atau memiliki nilai */
.search-input:focus + .search-placeholder,
.search-input:not(:placeholder-shown) + .search-placeholder {
  opacity: 0 !important;
}

.search-icon {
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #ee4d2d;
  cursor: pointer;
  font-size: 16px;
  z-index: 15;
  background: none;
  border: none;
  padding: 5px;
}

/* Clear search icon */
.clear-search-icon {
  position: absolute;
  right: 40px;
  top: 50%;
  transform: translateY(-50%);
  width: 20px;
  height: 20px;
  background-color: #999;
  border-radius: 50%;
  cursor: pointer;
  display: none;
  z-index: 35; /* Dinaikkan z-index-nya */
  align-items: center;
  justify-content: center;
  padding: 0;
  border: none;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
  overflow: visible; /* Pastikan tidak ada yang terpotong */
}

.clear-search-icon::before {
  content: '';
  position: absolute;
  width: 24px; /* Diperkecil dari 40px */
  height: 24px; /* Diperkecil dari 40px */
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: transparent; /* Pastikan background transparan */
}

.clear-search-icon svg {
  color: white;
  pointer-events: none;
  width: 12px;
  height: 12px;
  flex-shrink: 0; /* Pastikan SVG tidak mengecil */
}

.clear-search-icon:hover {
  background-color: #aaa;
}

.clear-search-icon:active {
  background-color: #999;
  transform: translateY(-50%) scale(0.95);
}

/* Show clear icon when input has value */
.search-input:not(:placeholder-shown) ~ .clear-search-icon {
  display: flex;
}

/* Adjust clear icon position in expanded mode */
.search-expanded .clear-search-icon {
  right: 15px !important; /* Posisi lebih ke kanan */
  display: flex !important;
  z-index: 40;
}

/* Header Icons */
.header-icons {
  display: flex;
  align-items: center;
  position: absolute;
  right: 8px; /* Geser lebih ke kanan */
  top: 50%;
  transform: translateY(-50%);
  z-index: 20;
  gap: 15px;
  padding: 15px 5px; /* Tambah padding untuk ruang badge */
  overflow: visible; /* Pastikan badge tidak terpotong */
}

/* Ensure header icons are always properly styled */
.header .header-icons,
header .header-icons {
  display: flex !important;
  align-items: center !important;
  position: absolute !important;
  right: 8px !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  z-index: 20 !important;
  gap: 15px !important;
  padding: 15px 5px !important;
  overflow: visible !important;
}

.cart-icon, .chat-icon {
  position: relative;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 22px;
  height: 22px;
}

/* Turunkan icon pada desktop dan tablet, tapi badge tetap di posisi semula */
@media (min-width: 769px) {
  .cart-icon, .chat-icon {
    transform: translateY(3px); /* Turunkan icon sedikit */
  }
}

.cart-icon svg, .chat-icon svg {
  width: 100%;
  height: 100%;
  overflow: visible; /* Mencegah pemotongan path SVG */
}

/* Badge notifikasi */
.cart-badge, .chat-badge {
  position: absolute;
  top: -12px;
  right: -6px;
  background-color: white;
  color: #ee4d2d;
  font-size: 11px;
  font-weight: bold;
  min-width: 20px;
  width: 20px;
  height: 20px;
  border-radius: 50%; /* Pastikan lingkaran sempurna */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  z-index: 30;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3);
  line-height: 1;
  /* Memastikan teks benar-benar rata tengah */
  text-align: center;
  box-sizing: border-box;
  /* Menghilangkan spasi ekstra di sekitar teks */
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  /* Pastikan tidak ada overflow yang memotong badge */
  overflow: visible;
}

/* Kompensasi badge agar tidak ikut turun pada desktop dan tablet, dan geser ke kanan */
@media (min-width: 769px) {
  .cart-badge, .chat-badge {
    transform: translateY(-3px) translateX(2px); /* Kompensasi agar badge tidak ikut turun dan geser ke kanan */
    right: -8px; /* Geser badge lebih ke kanan */
  }
}

/* Chat dots */
.chat-icon .chat-dots {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 60%;
  height: 20%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 10;
}

.chat-icon .chat-dots .dot {
  width: 2px;
  height: 2px;
  background-color: white;
  border-radius: 50%;
  flex-shrink: 0;
}

.chat-icon .chat-dots .dot:nth-child(1) {
  animation: dotPulse 1.5s infinite ease-in-out;
}

.chat-icon .chat-dots .dot:nth-child(2) {
  animation: dotPulse 1.5s infinite 0.2s ease-in-out;
}

.chat-icon .chat-dots .dot:nth-child(3) {
  animation: dotPulse 1.5s infinite 0.4s ease-in-out;
}

@keyframes dotPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

/* Search expanded mode */
.search-expanded {
  background-color: white;
  padding: 10px 15px 15px 15px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  z-index: 1001;
  box-sizing: border-box;
  min-height: 60px; /* Consistent with header height */
}

.back-btn {
  margin-right: 10px;
  color: #ee4d2d;
  cursor: pointer;
  display: flex;
  align-items: center;
  background: none;
  border: none;
  padding: 5px;
}

.back-btn svg {
  transform: scaleX(1.3);
}

.expanded-search-icon {
  background-color: #ee4d2d;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  cursor: pointer;
  border: none;
}

/* Filter icon styling - sesuai docs/facet.html */
.filter-icon {
  background-color: #ee4d2d;
  color: white;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
  cursor: pointer;
  border: none;
  position: relative;
  transition: all 0.2s ease;
}

.filter-icon i {
  font-size: 16px;
}

.filter-icon:hover {
  background-color: #d63916;
  transform: scale(1.05);
}

.filter-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #fff;
  color: #ee4d2d;
  border: 2px solid #ee4d2d;
  font-size: 11px;
  font-weight: bold;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 25;
  text-align: center;
  line-height: 1;
  box-sizing: border-box;
  padding: 0;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
}

/* Overlay area */
.overlay {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0px;
  width: 100%;
  height: 100%;
  background-color: white;
  z-index: 999;
  display: none;
  overflow-y: auto;
  box-sizing: border-box;
  pointer-events: none; /* Tidak menangkap klik, hanya sebagai background */
}

.overlay.show {
  display: block !important;
}

/* Enable page scroll when suggestions are shown - sesuai docs/facet.html */
body.show-suggestions {
  overflow-y: auto; /* Enable scroll pada halaman */
  position: static; /* Normal positioning untuk scroll */
  width: 100%;
  height: auto; /* Allow normal height */
}

/* Suggestions Container - Persis seperti docs/facet.html dengan scroll halaman */
.suggestions-container {
  background-color: white;
  width: 100%;
  max-width: 800px;
  margin: 0 auto;
  margin-top: 70px; /* Posisi sesuai facet.html */
  border-radius: 3px;
  overflow: visible; /* Tidak ada scroll di container */
  transition: all 0.3s ease;
  box-sizing: border-box;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  padding: 0;
  position: relative; /* Relative positioning untuk scroll halaman */
  z-index: 999; /* Di bawah header yang z-index 1000+ */
  pointer-events: auto; /* Pastikan container dapat menerima klik */
  height: auto; /* Auto height, scroll di halaman */
  min-height: auto; /* Auto height to fit content */
}

/* Keyword button container - sesuai docs/facet.html */
.keyword-button-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  padding: 15px;
  border-bottom: 1px solid #f2f2f2;
}

/* Keyword button styling - lebar menyesuaikan text dengan icon */
.keyword-button {
  display: inline-flex;
  align-items: center;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 16px;
  padding: 6px 12px;
  font-size: 13px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  width: auto; /* Auto width sesuai content */
  min-width: fit-content;
}

.keyword-button:hover {
  border-color: #ee4d2d;
  color: #ee4d2d;
  background-color: #fff9f8;
}

.keyword-button .suggestion-icon {
  margin-right: 6px;
  font-size: 12px;
  color: #999;
  display: flex;
  align-items: center;
}

.keyword-button .keyword-button-text {
  font-size: 13px;
  color: inherit;
}

/* Empty history message styling */
.empty-history-message {
  padding: 20px 15px;
  text-align: center;
  color: #999;
  font-size: 14px;
  border-bottom: 1px solid #f2f2f2;
}

.empty-history-message p {
  margin: 0;
}

/* Main keyword suggestions - SEBELUM klik "Lihat Lainnya" = bentuk tombol/tag */
.main-keyword-suggestions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
  border-bottom: 1px solid #f2f2f2;
}

.main-keyword-suggestion-tag {
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 12px;
  font-size: 13px;
  color: #333;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.main-keyword-suggestion-tag:hover {
  background-color: #ffe8e3;
  color: #ee4d2d;
}

/* Main keyword suggestions - SETELAH klik "Lihat Lainnya" = bentuk list */
.main-keyword-suggestions-list {
  padding: 0;
  border-bottom: 1px solid #f2f2f2;
}

.main-keyword-suggestions-list .suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.main-keyword-suggestions-list .suggestion-item:hover {
  background-color: #f9f9f9;
}

/* Main see more container styling */
.main-see-more-container {
  padding: 10px 0;
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
  z-index: 2;
  background-color: white;
  margin-bottom: 15px;
  display: block;
  border-bottom: 1px solid #f2f2f2;
}

.suggestion-item {
  padding: 12px 15px;
  cursor: pointer;
  display: flex;
  align-items: flex-start;
  border-bottom: 1px solid #f5f5f5;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover {
  background-color: #f9f9f9;
}

.suggestion-item:last-child {
  border-bottom: 1px solid #f5f5f5;
}

.suggestion-icon {
  margin-right: 12px;
  width: 20px;
  display: inline-flex;
  justify-content: center;
  color: #999;
  font-size: 14px;
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 1px;
}

.suggestion-icon i {
  font-size: 14px;
  color: #999;
}

.suggestion-text {
  display: inline-block;
  line-height: 1.4;
  word-break: break-word;
  color: #333;
  font-size: 14px;
}

/* Hide main content when suggestions are shown */
body.show-suggestions main,
body.show-suggestions .container,
body.show-suggestions section {
  display: none !important;
  visibility: hidden !important;
}

/* Main content spacing */
.main-content {
  padding-top: 80px; /* Space for fixed header */
  padding-bottom: 24px;
}

/* Visual spacing system - consistent 24px visual gap between components */
.visual-spacing-24 {
  margin-bottom: 24px; /* Consistent 24px visual spacing */
}

.visual-spacing-24:last-child {
  margin-bottom: 0; /* Remove margin from last section */
}

/* Override any internal margins/paddings that might interfere with visual spacing */
.visual-spacing-24 > * {
  margin-top: 0 !important; /* Remove top margins from children */
}

/* Ensure categories section has proper visual spacing */
.sellzio-categories-section {
  margin-top: 0 !important; /* Remove any top margin */
  margin-bottom: 0 !important; /* Remove any bottom margin */
}

/* Ensure live video section has proper visual spacing */
.container1 {
  margin-top: 0 !important; /* Remove any top margin */
  margin-bottom: 0 !important; /* Remove any bottom margin */
}

/* Ensure products section has proper visual spacing */
.sellzio-products-container {
  margin-top: 0 !important; /* Remove any top margin */
  margin-bottom: 0 !important; /* Remove any bottom margin */
}

@media (min-width: 768px) {
  .visual-spacing-24 {
    margin-bottom: 32px; /* Larger spacing on tablet */
  }
}

@media (min-width: 1024px) {
  .visual-spacing-24 {
    margin-bottom: 40px; /* Larger spacing on desktop */
  }
}

/* Ensure suggestions containers are always visible */
body.show-suggestions .suggestions-container {
  display: block !important;
  visibility: visible !important;
}

/* Clear history styling - sesuai docs/facet.html */
.clear-history {
  text-align: center;
  padding: 10px;
  margin-bottom: 5px;
  cursor: pointer;
  color: #999;
  font-size: 13px;
  border-bottom: 1px solid #f2f2f2;
  transition: color 0.2s ease;
}

.clear-history:hover {
  color: #ee4d2d;
}

/* Extended suggestions styling - sesuai docs/facet.html */
.extended-suggestions {
  padding: 0;
  border-bottom: 1px solid #f2f2f2;
}

.extended-suggestions .suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
}

.extended-suggestions .suggestion-item:hover {
  background-color: #f9f9f9;
}

/* Trend pill styling - persis sesuai docs/facet.html */
.trend-pill {
  display: inline-flex;
  align-items: center;
  background-color: #fff4f2;
  color: #ee4d2d;
  font-size: 16px;
  font-weight: 500;
  padding: 6px 15px;
  border-radius: 15px;
  margin: 20px 0 20px 15px;
}

.trend-pill-badge {
  background-color: #ee4d2d;
  color: white;
  font-size: 10px;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 8px;
  text-align: center;
}

/* Trending section styling */
.trending-section {
  padding: 15px 0;
  border-bottom: 1px solid #f2f2f2;
}

/* Keyword suggestion tags - SEBELUM klik "Lihat Lainnya" = bentuk tombol/tag */
.keyword-suggestions-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 10px;
  width: 100%;
  padding: 15px;
  box-sizing: border-box;
}

.keyword-suggestion-tag {
  background-color: #f5f5f5;
  border-radius: 20px;
  padding: 8px 12px;
  font-size: 13px;
  color: #333;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  outline: none;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.keyword-suggestion-tag:hover {
  background-color: #ffe8e3;
  color: #ee4d2d;
}

/* Keyword suggestions list - SETELAH klik "Lihat Lainnya" = bentuk list */
.keyword-suggestions-list {
  padding: 0;
}

.keyword-suggestions-list .suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
  transition: background-color 0.2s ease;
  border-bottom: none;
}

.keyword-suggestions-list .suggestion-item:hover {
  background-color: #f9f9f9;
}

.keyword-suggestions-list .suggestion-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 14px;
  color: #999;
}

.keyword-suggestions-list .suggestion-text {
  font-size: 14px;
  color: #333;
}

/* Trending items styling */
.trending-item .suggestion-icon i {
  color: #ee4d2d !important;
}

/* Keyword suggestions popup styling - sesuai docs/facet.html */
.keyword-suggestions-popup {
  padding: 15px 0;
  border-top: 1px solid #f2f2f2;
  margin-top: 15px;
}

.keyword-suggestions-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  padding: 0 15px;
  font-weight: 500;
}

/* Trending title styling - sesuai docs/facet.html */
.trending-title {
  margin-top: 15px;
  color: #333;
  font-weight: 500;
  padding: 12px 15px 5px;
  font-size: 13px;
  display: flex;
  align-items: center;
}

.trending-title .trend-icon {
  margin-left: 5px;
  font-size: 13px;
  color: #ee4d2d;
}

/* Additional suggestions styling */
.additional-suggestions {
  max-height: none;
  overflow: visible;
  transition: max-height 0.3s ease;
  border-bottom: 0px solid #f2f2f2;
  background-color: white;
  position: relative;
  z-index: 1;
  margin-bottom: 15px;
  display: block;
}

.additional-suggestions.open {
  max-height: none;
  border-bottom: 1px solid #f2f2f2;
  padding-bottom: 20px;
}

.additional-suggestions .suggestion-item {
  padding: 10px 15px;
  cursor: pointer;
  display: flex;
  align-items: center;
}

.additional-suggestions .suggestion-item:hover {
  background-color: #f9f9f9;
}

.additional-suggestions .suggestion-icon {
  font-size: 18px !important;
  margin-bottom: 6px;
  color: #ee4d2d; /* Warna oranye untuk ikon trend */
}

/* See more button styling - persis seperti docs/facet.html */
.see-more-container {
  padding: 10px 0;
  text-align: center;
  transition: all 0.2s ease;
  position: relative;
  z-index: 2;
  background-color: white;
  margin-bottom: 15px;
  display: block;
  border-bottom: 1px solid #f2f2f2;
}

.see-more-btn {
  background-color: transparent;
  color: #ee4d2d;
  border: none;
  padding: 8px 15px;
  font-size: 14px;
  cursor: pointer;
  border-radius: 4px;
  font-weight: 500;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
  gap: 5px;
}

.see-more-btn:hover {
  background-color: #f9f9f9;
}

.see-more-btn i {
  color: #ee4d2d;
  font-size: 14px;
}

/* Keyword Predictions Container - sesuai docs/facet.html */
.keyword-predictions {
  position: absolute;
  top: 50px;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  z-index: 1000;
  max-width: 800px;
  margin: 10px auto;
  transform: translateY(10px);
  /* Hapus max-height dan overflow untuk menghilangkan scroll internal */
  height: auto;
  overflow: visible;
}

/* Hapus scrollbar styling karena tidak ada scroll internal */

/* Prediction item styling */
.prediction-item {
  padding: 10px 15px;
  cursor: pointer;
  border-bottom: 1px solid #f5f5f5;
  display: flex;
  align-items: flex-start;
  transition: background-color 0.2s ease;
}

.prediction-item:hover {
  background-color: #f9f9f9;
}

.prediction-item:last-child {
  border-bottom: none;
}

/* Prediction icon styling */
.prediction-icon {
  margin-right: 12px;
  width: 20px;
  display: inline-flex;
  justify-content: center;
  color: #999;
  flex-shrink: 0;
  align-self: flex-start;
  margin-top: 2px;
}

/* Matched prediction icon (orange color) */
.prediction-icon.matched {
  color: #ee4d2d;
}

/* Prediction text styling */
.prediction-text {
  font-size: 14px;
  color: #333;
  line-height: 1.4;
  word-break: break-word;
}

/* Highlighted text in predictions */
.prediction-text .highlighted {
  color: #ee4d2d;
  font-weight: bold;
}

/* Responsive styling for predictions - sesuai docs/facet.html */
@media (min-width: 768px) {
  .keyword-predictions {
    top: 50px;
    border-radius: 8px;
    margin-top: 25px;
    /* Pastikan tidak ada scroll internal di desktop */
    overflow: visible;
    height: auto;
  }
}

/* Pastikan body dapat scroll saat predictions muncul */
body.show-suggestions {
  overflow-y: auto !important;
  position: static !important;
  width: 100% !important;
  height: auto !important;
}

/* Search Results Container - sesuai docs/facet.html */
.search-results-container {
  position: absolute;
  top: 60px;
  background-color: white !important;
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
  z-index: 1000;
  max-width: calc(100% - 80px);
  margin: 10px 60px 10px auto;
  transform: translateY(10px);
  height: auto;
  overflow: visible;
  padding: 20px;
}

.search-results-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 15px;
}

.search-results-header h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.results-count {
  font-size: 14px;
  color: #666;
}

/* Filter Tabs Styling - sesuai docs/facet.html */
.filter-tabs-container {
  margin: 20px 0;
  border-bottom: 1px solid #f0f0f0;
}

.filter-tabs {
  display: flex;
  gap: 0;
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.filter-tabs::-webkit-scrollbar {
  display: none;
}

.filter-tab {
  background: none;
  border: none;
  padding: 12px 20px;
  font-size: 14px;
  font-weight: 500;
  color: #666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  white-space: nowrap;
  min-width: fit-content;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.filter-tab:hover {
  color: #ff6b35;
  background-color: #fff5f2;
}

.filter-tab.active {
  color: #ff6b35;
  border-bottom-color: #ff6b35;
  font-weight: 600;
}

.filter-tab.active::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #ff6b35;
  border-radius: 1px 1px 0 0;
}

/* No Results Styling */
.no-results {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.no-results-icon {
  font-size: 48px;
  color: #ddd;
  margin-bottom: 20px;
}

.no-results h4 {
  margin: 0 0 10px 0;
  font-size: 18px;
  color: #333;
}

.no-results p {
  margin: 0;
  font-size: 14px;
  color: #666;
}

/* Search Results Grid */
.search-results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.search-result-card {
  border: 1px solid #f0f0f0; /* Restore individual card border */
  border-radius: 8px;
  padding: 15px;
  background-color: white; /* Restore white background for cards */
  transition: box-shadow 0.2s ease;
  position: relative;
  cursor: pointer;
}

.search-result-card:hover {
  box-shadow: 0 4px 12px rgba(0,0,0,0.1); /* Restore original hover effect */
}

.result-product-img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 6px;
  margin-bottom: 12px;
}

.result-product-info {
  margin-bottom: 10px;
}

.result-product-name {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.sellzio-mall-icon {
  background-color: #ee4d2d;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 3px;
  margin-right: 6px;
  font-weight: 600;
}

.result-product-rating-sold {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.result-product-rating {
  display: flex;
  align-items: center;
  font-size: 12px;
  color: #666;
}

.result-product-rating i {
  color: #ffa500;
  margin-right: 4px;
}

.result-product-sold {
  font-size: 12px;
  color: #666;
}

.result-product-shipping {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.result-product-shipping i {
  margin-right: 4px;
  color: #00b14f;
}

.result-product-location {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.result-product-location i {
  margin-right: 4px;
  color: #ee4d2d;
}

.result-product-price-container {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.result-product-price {
  font-size: 16px;
  font-weight: 600;
  color: #ee4d2d;
}

.result-product-price-original {
  font-size: 12px;
  color: #999;
  text-decoration: line-through;
}

.result-product-discount {
  font-size: 12px;
  color: #ee4d2d;
  background-color: #fff2f0;
  padding: 2px 6px;
  border-radius: 3px;
}

.result-cod-icon {
  position: absolute;
  top: 10px;
  right: 10px;
  background-color: #00b14f;
  color: white;
  font-size: 10px;
  padding: 4px 6px;
  border-radius: 3px;
  font-weight: 600;
}

.result-cod-icon i {
  margin-right: 3px;
}

/* Hide main content when search results are shown - sesuai docs/facet.html */
.hide-main-content .main-content {
  display: none !important;
}

.hide-main-content .header-content {
  display: none !important;
}

.hide-main-content .category-section {
  display: none !important;
}

.hide-main-content .hero-section {
  display: none !important;
}

.hide-main-content .product-section {
  display: none !important;
}

.hide-main-content .footer {
  display: none !important;
}

/* Ensure white background for search results - no black overlay */
.hide-main-content {
  background-color: white !important;
}

/* Remove any dark background when search results are shown */
body.hide-main-content {
  background-color: white !important;
}

/* Ensure search results container has full white background */
.search-results-container {
  background-color: white !important;
  min-height: auto !important;
}

/* Responsive untuk mobile - sesuai docs/facet.html */
@media (max-width: 767px) {
  .header {
    padding-top: 15px;
    padding-bottom: 20px;	/* Tambahkan padding atas lebih besar untuk mobile */
  }

  .header .header-icons {
    top: 50%;
    transform: translateY(-50%);
    padding-top: 10px;
    gap: 5px; /* Gap lebih kecil untuk mobile */
  }
  
  /* Media query untuk mobile tidak perlu mengatur ulang clear search icon karena sudah diatur di atas */
  
  .search-expanded .search-input {
    padding-right: 45px; /* Ruang untuk tombol clear search */
  }

  .search-results-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
  }

  .search-results-container {
    margin: 5px;
    padding: 15px;
  }
}

/* Product suggestions styling - sesuai docs/facet.html */
.product-suggestions {
  padding: 10px;
  width: 100%;
  box-sizing: border-box;
  margin-top: 10px;
}

.product-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
  padding: 0 5px;
  display: block;
}

.product-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr); /* Mobile: 2 kolom */
  gap: 10px;
  width: 100%;
  box-sizing: border-box;
  border: 2px solid #eee;
  border-radius: 5px;
  margin-top: 30px;
  padding: 10px 10px 25px 10px;
  transform: translateY(0px);
}

/* Tablet: 3 kolom - sesuai docs/facet.html */
@media (min-width: 768px) and (max-width: 1023px) {
  .header, .search-expanded {
    padding: 15px;
    width: 100%;
    box-sizing: border-box;
  }

  .product-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
  }
}

/* Desktop: 4 kolom - sesuai docs/facet.html */
@media (min-width: 1024px) {
  .desktop-view .header {
    padding: 15px;
    display: flex;
    justify-content: center;
    align-items: center;
    overflow: hidden;
  }

  .desktop-view .search-container {
    max-width: 800px;
    margin: 0 auto;
    width: 100%;
  }

  .desktop-view .search-expanded {
    display: flex;
    justify-content: center;
  }

  .desktop-view .search-expanded .search-container {
    width: 100%;
    max-width: 800px;
  }

  .product-grid {
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
  }
}

.simple-product-card {
  background-color: white;
  border-radius: 3px;
  transform: translateY(10px);
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0,0,0,0.15);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  text-align: center;
}

.simple-product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.2);
}

.product-img {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

.simple-product-name {
  font-size: 13px;
  color: #333;
  padding: 10px 5px;
  text-align: center;
  height: auto;
  max-height: 40px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* Facet overlay untuk mobile */
.facet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(0,0,0,0.5);
  z-index: 1050;
  display: none;
}

/* Panel facet untuk mobile/tablet */
.facet-panel {
  background-color: white;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  border-radius: 10px 10px 0 0;
  overflow-y: auto;
  padding: 20px;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  margin: 0 auto;
}

/* Panel facet untuk desktop */
.facet-panel-desktop {
  position: fixed;
  right: 0;
  top: 0;
  height: 100%;
  width: 350px;
  background-color: white;
  box-shadow: -2px 0 10px rgba(0,0,0,0.1);
  z-index: 1050;
  padding: 20px;
  overflow-y: auto;
  display: none;
}

.facet-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #f0f0f0;
}

.facet-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.facet-close {
  cursor: pointer;
  color: #666;
  font-size: 18px;
  padding: 5px;
}

.facet-close:hover {
  color: #ee4d2d;
}

.facet-content-wrapper {
  margin-bottom: 20px;
}

.facet-buttons {
  display: flex;
  gap: 10px;
  padding-top: 15px;
  border-top: 1px solid #f0f0f0;
}

.facet-button {
  flex: 1;
  padding: 12px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.facet-button-reset {
  background-color: #f5f5f5;
  color: #666;
}

.facet-button-reset:hover {
  background-color: #e0e0e0;
}

.facet-button-apply {
  background-color: #ee4d2d;
  color: white;
}

.facet-button-apply:hover {
  background-color: #d63916;
}

/* Responsive untuk facet */
@media (min-width: 1025px) {
  .facet-panel-desktop {
    display: block;
  }
  .facet-overlay {
    display: none !important;
  }
}



/* Facet Section Styling - sesuai docs/facet.html */
.facet-section {
  margin-bottom: 20px;
  padding: 15px;
  background-color: white;
  border-bottom: 1px solid #f2f2f2;
}

.facet-section h3 {
  font-size: 16px;
  color: #333;
  margin-bottom: 15px;
  font-weight: 600;
  padding: 0;
}

.facet-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.facet-section li {
  padding: 6px 0;
  display: flex;
  align-items: flex-start;
}

.facet-section input[type="checkbox"] {
  margin-right: 12px;
  flex-shrink: 0;
  margin-top: 1px;
  align-self: flex-start;
  position: relative;
}

.facet-section label {
  font-size: 14px;
  color: #666;
  cursor: pointer;
  flex: 1;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
  display: block;
  align-self: flex-start;
}

.facet-section label:hover {
  color: #ee4d2d;
}

.facet-section input[type="checkbox"]:checked + label {
  color: #ee4d2d;
  font-weight: 500;
}

.facet-section input[type="checkbox"]:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.facet-section input[type="checkbox"]:disabled + label {
  opacity: 0.5;
  cursor: not-allowed;
  color: #999;
}

/* Orange checkbox styling */
.orange-checkbox {
  position: relative;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  width: 18px;
  height: 18px;
  border: 1px solid #ccc;
  border-radius: 3px;
  outline: none;
  transition: all 0.2s ease;
  vertical-align: middle;
  margin-right: 12px;
}

.orange-checkbox:checked {
  background-color: #ee4d2d;
  border-color: #ee4d2d;
}

.orange-checkbox:checked::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 3px;
  width: 5px;
  height: 9px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Styling untuk label saat checkbox di-check */
.orange-checkbox:checked + label {
  color: #ee4d2d;
  font-weight: 500;
}

/* Disabled checkbox styling */
.orange-checkbox:disabled {
  background-color: #f5f5f5;
  border-color: #ddd;
  cursor: not-allowed;
  opacity: 0.6;
}

.orange-checkbox:disabled:checked {
  background-color: #ccc;
  border-color: #999;
}

.orange-checkbox:disabled + label {
  color: #999;
  cursor: not-allowed;
}

/* Category and subcategory styling for facet panel */
.facet-section .category-name {
  font-weight: 600;
  color: #333;
  font-size: 14px;
  text-align: left;
}

/* Disabled category styling */
.facet-section .category-name.disabled {
  color: #999;
  opacity: 0.6;
  cursor: not-allowed;
}

/* Disabled subcategory label styling */
.facet-section .subcategory-item label.disabled {
  color: #999;
  opacity: 0.6;
  cursor: not-allowed;
}

/* Multi-category hierarchical styling */
.facet-section .category-group {
  margin-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 12px;
}

.facet-section .category-group:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.facet-section .main-category {
  margin-bottom: 8px;
  padding: 4px 0;
}

.facet-section .main-category label {
  font-weight: 600;
  color: #333;
  font-size: 14px;
}

.facet-section .main-category label strong {
  font-weight: 700;
}

/* Subcategory indentation in multi-category mode */
.facet-section .category-group .subcategory-item {
  margin-left: 20px;
  padding: 6px 0;
  border-bottom: none;
}

/* Multi-category dropdown styling - sama seperti provinsi */
.facet-section .category-item-with-dropdown {
  margin-bottom: 8px;
}

.facet-section .category-header {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  min-height: 24px;
}

.facet-section .category-header input[type="checkbox"] {
  margin-right: 12px;
  flex-shrink: 0;
  margin-top: 1px;
  align-self: flex-start;
}

.facet-section .category-header label {
  font-size: 14px;
  color: #666;
  cursor: pointer;
  flex: none; /* Changed from flex: 1 to flex: none so button follows immediately */
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
  display: inline; /* Changed from block to inline so button can follow immediately */
  align-self: flex-start;
  margin: 0;
  padding: 0;
  font-weight: normal; /* Same as province label - remove bold */
}

.facet-section .category-header label strong {
  font-weight: normal; /* Remove bold from strong tag to match province */
}

.facet-section .subcategory-dropdown-toggle {
  background: none;
  border: none;
  color: #ee4d2d;
  cursor: pointer;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 3px;
  transition: background-color 0.2s;
  margin-left: 8px; /* Small space from text */
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
}

.facet-section .subcategory-dropdown-toggle:hover {
  background-color: #f5f5f5;
}

/* Subcategory dropdown styling - checkbox lurus dengan awal huruf kategori */
.facet-section .subcategory-dropdown {
  margin-left: 0px !important; /* Set ke 0 agar checkbox subcategory lurus dengan awal huruf kategori */
  margin-top: 8px;
  padding-left: 0 !important;
  list-style: none;
  width: 100% !important; /* Full width karena tidak ada indentasi */
}

.facet-section .subcategory-dropdown-item {
  margin-left: 0 !important; /* Reset any inherited margin */
  padding-left: 0 !important;
  margin-bottom: 6px;
  padding: 8px 0;
  border: none;
  background: none;
  display: flex !important;
  flex-direction: row !important;
  align-items: flex-start !important;
  min-height: 24px;
}

.facet-section .subcategory-dropdown-item input[type="checkbox"] {
  margin-right: 12px;
  margin-left: 0 !important; /* Pastikan tidak ada margin kiri */
  flex-shrink: 0;
  margin-top: 1px;
  align-self: flex-start;
}

.facet-section .subcategory-dropdown-item label {
  font-size: 13px;
  color: #666;
  text-align: left;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  flex: 1;
  margin: 0;
  padding: 0;
  display: block !important;
  align-self: flex-start;
  cursor: pointer;
  font-weight: normal;
}

/* Pastikan semua subcategory dropdown item lurus dengan kategori */
ul.subcategory-dropdown {
  margin-left: 0 !important; /* Pastikan tidak ada margin kiri */
  padding-left: 0 !important;
}

ul.subcategory-dropdown li {
  margin-left: 0 !important; /* Pastikan semua li tidak ada margin kiri */
  padding-left: 0 !important;
}

/* Subcategory expand button styling */
.subcategory-expand-button {
  margin-top: 8px;
  padding: 0;
  margin-left: 16px; /* Align with subcategory items */
}

/* Province expand button styling */
.province-expand-button {
  margin-top: 8px;
  padding: 0;
}

/* Province item styling */
.province-item {
  margin-bottom: 6px;
  padding: 8px 0;
  display: flex !important;
  flex-direction: column !important;
  align-items: flex-start !important;
}

.province-header {
  display: flex;
  align-items: center;
  width: 100%;
  position: relative;
  min-height: 24px;
}

.province-header input[type="checkbox"] {
  margin-right: 12px;
  flex-shrink: 0;
  margin-top: 1px;
  align-self: flex-start;
}

.province-header label {
  font-size: 14px;
  color: #666;
  cursor: pointer;
  flex: none; /* Changed from flex: 1 to flex: none so button follows immediately */
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
  display: inline; /* Changed from block to inline so button can follow immediately */
  align-self: flex-start;
  margin: 0;
  padding: 0;
}

.city-dropdown-toggle {
  background: none;
  border: none;
  color: #ee4d2d;
  cursor: pointer;
  font-size: 12px;
  padding: 2px 4px;
  border-radius: 3px;
  transition: background-color 0.2s;
  margin-left: 8px; /* Small space from text */
  flex-shrink: 0;
  display: inline-flex;
  align-items: center;
}

.city-dropdown-toggle:hover {
  background-color: #f5f5f5;
}

/* City dropdown styling - indented to the right like subcategories */
.city-dropdown {
  margin-left: 28px !important; /* Geser ke kiri sedikit */
  margin-top: 8px;
  padding-left: 0 !important;
  list-style: none;
  width: calc(100% - 28px) !important; /* Adjust width to account for indentation */
}

.facet-section .city-item {
  margin-left: 0 !important; /* Reset any inherited margin */
  padding-left: 0 !important;
}

/* CITY INDENTATION - FINAL VERSION */
ul.city-dropdown {
  margin-left: 28px !important; /* Geser ke kiri sedikit dari 50px ke 28px */
  padding-left: 0 !important;
}

.city-item {
  margin-bottom: 6px;
  padding: 8px 0;
  border: none;
  background: none;
  display: flex !important;
  flex-direction: row !important;
  align-items: flex-start !important;
  min-height: 24px;
}

.city-item input[type="checkbox"] {
  margin-right: 12px;
  flex-shrink: 0;
  margin-top: 1px;
  align-self: flex-start;
}

.city-item label {
  font-size: 13px;
  color: #666;
  text-align: left;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  flex: 1;
  margin: 0;
  padding: 0;
  display: block !important;
  align-self: flex-start;
  cursor: pointer;
  font-weight: normal;
}

.expand-button {
  background: none;
  border: none;
  color: #ee4d2d;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 0;
  display: flex;
  align-items: center;
  justify-content: flex-start; /* Align to left instead of space-between */
  width: auto; /* Auto width instead of 100% */
  text-align: left;
  transition: color 0.2s ease;
  gap: 6px; /* Add gap between text and arrow */
}

.expand-button:hover {
  color: #d73211;
}

.expand-arrow {
  font-size: 10px;
  margin-left: 0; /* Remove margin since we use gap in parent */
  transition: transform 0.2s ease;
}

.expand-arrow.up {
  transform: rotate(0deg);
}

.expand-arrow.down {
  transform: rotate(0deg);
}

/* Subcategory item spacing */
.subcategory-item {
  margin-bottom: 6px;
}

.subcategory-item:last-child {
  margin-bottom: 0;
}

.facet-section .subcategory-item {
  margin-left: 16px;
  padding: 8px 0;
  border: none;
  background: none;
  display: flex !important;
  flex-direction: row !important;
  align-items: flex-start !important;
  min-height: 24px;
}

.facet-section .subcategory-item label {
  font-size: 13px;
  color: #666;
  text-align: left;
  line-height: 1.3;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  flex: 1;
  margin: 0;
  padding: 0;
  display: block !important;
  align-self: flex-start;
}

/* Ensure checkbox is always on the left side for facet subcategories */
.facet-section .subcategory-item input[type="checkbox"] {
  order: 1;
  margin-right: 12px;
  margin-left: 0;
  margin-top: 1px;
  flex-shrink: 0;
  align-self: flex-start;
}

.facet-section .subcategory-item label {
  order: 2;
}

.facet-section .subcategory-item:hover {
  transform: none;
  box-shadow: none;
  border: none;
  background: none;
}

/* Active Filters Styling */
.active-filters {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 15px;
}

.filter-tag {
  background-color: #f5f5f5;
  border-radius: 16px;
  padding: 5px 10px;
  font-size: 12px;
  color: #333;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.filter-tag i {
  margin-left: 6px;
  color: #666;
  cursor: pointer;
  font-size: 11px;
  padding: 2px;
  border-radius: 50%;
  transition: all 0.2s ease;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.filter-tag i:hover {
  color: #ee4d2d;
  background-color: rgba(238, 77, 45, 0.1);
}

/* Facet overlay untuk mobile */
.facet-overlay {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1050;
  display: none;
  justify-content: center;
  align-items: flex-end;
}

/* Panel facet untuk mobile/tablet */
.facet-panel {
  background-color: white;
  width: 100%;
  max-width: 500px;
  max-height: 80vh;
  border-radius: 10px 10px 0 0;
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  margin: 0 auto;
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from { transform: translateY(100%); }
  to { transform: translateY(0); }
}

/* Panel facet untuk desktop */
.facet-panel-desktop {
  position: fixed;
  right: 0;
  top: 0;
  height: 100%;
  width: 350px;
  background-color: white;
  box-shadow: -2px 0 10px rgba(0,0,0,0.1);
  z-index: 1050;
  display: none;
  flex-direction: column;
  animation: slideIn 0.3s ease;
}

@keyframes slideIn {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

/* Animation for tablet panel sliding from right */
@keyframes slideInFromRight {
  from { transform: translateX(100%); }
  to { transform: translateX(0); }
}

.facet-header {
  position: sticky;
  top: 0;
  display: flex;
  background-color: white;
  z-index: 10;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  border-bottom: 1px solid #f2f2f2;
}

.facet-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.facet-close {
  cursor: pointer;
  color: #999;
  font-size: 18px;
  padding: 5px;
}

.facet-close:hover {
  color: #ee4d2d;
}

.facet-content-wrapper {
  flex: 1;
  overflow-y: auto;
  padding: 0 15px;
  max-height: calc(80vh - 120px);
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
}

.facet-buttons {
  display: flex;
  gap: 10px;
  padding: 15px;
  border-top: 1px solid #f2f2f2;
  background-color: white;
}

.facet-button {
  flex: 1;
  padding: 12px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  text-align: center;
}

.facet-button-reset {
  background-color: #f5f5f5;
  color: #666;
}

.facet-button-reset:hover {
  background-color: #e8e8e8;
}

.facet-button-apply {
  background-color: #ee4d2d;
  color: white;
}

.facet-button-apply:hover {
  background-color: #d63916;
}

/* Tambahkan ruang kosong setelah konten facet terakhir */
.facet-section:last-child {
  margin-bottom: 30px;
  border-bottom: none;
}

/* Mencegah scroll pada body saat facet panel aktif */
body.facet-active {
  overflow: hidden;
}

/* Pastikan panel facet memiliki padding yang tepat */
.facet-panel, .facet-panel-desktop {
  overscroll-behavior: contain;
  padding: 0;
}

/* Desktop Sidebar Layout */
.search-results-layout {
  display: flex;
  gap: 0; /* Remove gap so sidebar sticks to product container */
  width: 100%;
  max-width: none; /* Full page width for desktop */
  margin: 0 20px 20px 20px; /* Add side margins instead of centering */
  padding: 0; /* Remove internal padding to make components truly stick together */
  background-color: white; /* Add background to create unified container look */
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1); /* Add subtle shadow for container effect */
  border: 1px solid #e5e5e5;
  overflow: visible; /* Ubah dari hidden ke visible agar konten facet bisa scroll */
}

/* Desktop Facet Sidebar */
.desktop-facet-sidebar {
  width: 280px;
  flex-shrink: 0;
  display: none; /* Hidden on mobile/tablet */
  margin-right: 0; /* Ensure no margin */
  padding-top: 20px; /* Add spacing from header */
}

.facet-sidebar-desktop {
  background-color: white; /* Same background as product area */
  border: none; /* Remove border since parent container has border */
  border-radius: 8px 0 0 8px; /* Round only left corners to match container */
  border-right: 1px solid #e5e5e5; /* Add separator line between sidebar and products */
  padding: 0; /* Remove padding, let child elements handle their own padding */
  height: auto; /* Auto height instead of 100% to prevent scroll */
  min-height: auto; /* Remove minimum height to prevent scroll */
  max-height: none; /* Remove max height restriction */
  overflow: visible; /* Remove scroll effect */
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

/* Desktop sidebar specific header styling */
.facet-sidebar-desktop .facet-header {
  background-color: white; /* Same background as product area */
  padding: 20px 20px 15px 20px;
  border-bottom: 1px solid #e5e5e5;
  border-radius: 8px 0 0 0; /* Round only top-left corner */
  margin-bottom: 0;
}

/* Filter icon styling for desktop sidebar */
.facet-sidebar-desktop .facet-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.facet-filter-icon {
  color: #666;
  flex-shrink: 0;
}

/* Desktop sidebar specific content styling */
.facet-sidebar-desktop .facet-content-wrapper {
  flex: 1;
  padding: 0 20px;
  background-color: white; /* Same background as product area */
  overflow: visible; /* Remove scroll effect */
  max-height: none; /* Remove height restriction */
}

/* Desktop sidebar specific content */
.facet-sidebar-desktop .facet-content {
  overflow: visible; /* Remove scroll effect */
  max-height: none; /* Remove height restriction */
}

/* Desktop sidebar specific buttons styling */
.facet-sidebar-desktop .facet-buttons {
  padding: 15px 20px 20px 20px;
  background-color: white; /* Same background as product area */
  border-radius: 0 0 0 8px; /* Round only bottom-left corner */
}

/* Search Results Content Area - Override global styles */
.search-results-layout .search-results-container {
  flex: 1;
  margin: 0 !important;
  padding: 20px 60px 20px 20px !important;
  background-color: transparent !important; /* Remove background to prevent double container effect */
  border: none !important; /* Remove border since parent container has border */
  border-radius: 0 !important; /* Remove border radius to prevent double container effect */
  min-height: 500px !important; /* Ensure minimum height to match sidebar */
  box-sizing: border-box !important;
  position: static !important; /* Override absolute positioning */
  top: auto !important;
  left: auto !important;
  right: 0 !important;
  width: calc(100% - 60px) !important;
  max-width: none !important; /* Hapus batasan max-width */
}

/* Adjust grid for sidebar layout */
.search-results-layout .desktop-categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 8px;
  padding: 12px;
  max-width: 100%;
  margin: 0 auto;
  border: 1px solid #e5e7eb; /* border-gray-200 */
  border-radius: 0.5rem; /* rounded-lg */
  background-color: white;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1); /* shadow-sm */
}

/* Desktop responsive */
@media (min-width: 1024px) {
  .desktop-facet-sidebar {
    display: block;
    position: sticky;
    top: 80px; /* Memberikan jarak dari header */
    height: auto;
    z-index: 10;
  }
  
  /* Khusus desktop - container yang bisa di-scroll */
  .search-results-layout .search-results-container {
    padding-right: 80px !important;
    width: calc(100% - 80px) !important;
    max-height: none; /* Remove max height restriction */
    overflow-y: visible; /* Remove scroll, let page scroll naturally */
  }
  
  .search-results-container {
    max-width: calc(100% - 100px);
    margin-right: 100px;
  }

  .search-results-layout .search-results-grid {
    grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
    gap: 20px;
  }
  
  .search-results-layout {
    min-height: auto; /* Auto height to fit content */
    align-items: flex-start; /* Untuk memastikan item mulai dari atas */
  }
  
  /* Panel facet tidak boleh scroll */
  .facet-sidebar-desktop .facet-content-wrapper {
    overflow: visible;
    max-height: none;
  }
}

/* Tablet responsive */
@media (min-width: 768px) and (max-width: 1023px) {
  .search-results-layout {
    flex-direction: column;
    margin: 0 !important;
    max-width: 100% !important;
    width: 100% !important;
    padding: 0 !important;
  }

  .desktop-facet-sidebar {
    display: none;
  }

  .search-results-layout .search-results-container {
    background-color: white !important; /* Restore background for tablet */
    border-radius: 0 !important;
    min-height: 300px !important; /* Adjust minimum height for tablet */
    max-width: 100% !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 10px !important;
    border: none !important;
  }

  .search-results-container {
    max-width: 100% !important;
    margin: 0 !important;
    width: 100% !important;
    padding: 10px !important;
    right: 0 !important;
    left: 0 !important;
  }

  .search-results-layout .search-results-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 15px;
  }

  /* Tablet specific facet panel - slide from right */
  .facet-overlay {
    justify-content: flex-end !important; /* Align panel to right */
    align-items: stretch !important; /* Full height */
  }

  .facet-panel {
    max-width: 400px !important; /* Fixed width for tablet */
    width: 400px !important; /* Fixed width instead of full width */
    height: 100vh !important; /* Full height */
    max-height: 100vh !important; /* Full height */
    margin: 0 !important; /* Remove margins */
    border-radius: 0 !important; /* Remove border radius */
    animation: slideInFromRight 0.3s ease !important; /* Slide from right animation */
    position: relative !important;
    right: 0 !important;
    box-shadow: -2px 0 10px rgba(0,0,0,0.1) !important; /* Add shadow like desktop */
  }

  /* Adjust content wrapper for tablet full height */
  .facet-panel .facet-content-wrapper {
    max-height: calc(100vh - 120px) !important; /* Adjust for full height */
  }
}

/* Not Found Container - sesuai docs/facet.html */
.not-found-container {
  text-align: center;
  padding: 30px 20px 40px;
  margin: 0;
  background-color: #fff;
  width: 100%;
  position: relative;
}

.not-found-icon {
  margin: 0 auto 20px;
  width: 120px;
  height: 120px;
  opacity: 0.5;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Search Document Icon - sesuai docs/facet.html */
.search-document-icon {
  position: relative;
  width: 80px;
  height: 100px;
}

.document-base {
  width: 60px;
  height: 80px;
  background-color: #ddd;
  border-radius: 4px;
  position: absolute;
  top: 10px;
  left: 10px;
}

.document-fold {
  width: 15px;
  height: 15px;
  background-color: #bbb;
  position: absolute;
  top: 10px;
  right: 10px;
  clip-path: polygon(0 0, 0 100%, 100% 100%);
}

.document-lines {
  position: absolute;
  top: 25px;
  left: 20px;
  width: 40px;
}

.document-lines::before,
.document-lines::after {
  content: '';
  display: block;
  width: 100%;
  height: 2px;
  background-color: #bbb;
  margin-bottom: 5px;
}

.magnifying-glass {
  width: 30px;
  height: 30px;
  border: 3px solid #999;
  border-radius: 50%;
  position: absolute;
  bottom: -5px;
  right: -5px;
}

.magnifying-glass::after {
  content: '';
  width: 3px;
  height: 12px;
  background-color: #999;
  position: absolute;
  bottom: -15px;
  right: -5px;
  border-radius: 10px;
  transform: rotate(45deg);
}

.not-found-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
}

.not-found-message {
  font-size: 14px;
  color: #888;
  margin-bottom: 25px;
}

.not-found-button-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
  margin: 0 auto;
}

.not-found-button {
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.not-found-button.primary {
  background-color: #ee4d2d;
  color: white;
}

.not-found-button.primary:hover {
  background-color: #e04224;
}

.not-found-button.secondary {
  background-color: white;
  color: #ee4d2d;
  border: 1px solid #ee4d2d;
}

.not-found-button.secondary:hover {
  background-color: rgba(238, 77, 45, 0.05);
}

/* Mobile responsive untuk not found */
@media (max-width: 576px) {
  .not-found-container {
    padding: 20px 15px 30px;
    margin: 10px 0;
  }

  .not-found-button-container {
    max-width: 100%;
  }

  .not-found-message {
    max-width: 95%;
  }

  .not-found-icon {
    width: 100px;
    height: 100px;
  }

  .not-found-button {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/* Mobile responsive */
@media (max-width: 767px) {
  .search-results-layout {
    flex-direction: column;
    margin: 0 !important;
    max-width: 100% !important;
    width: 100% !important;
    padding: 0 !important;
  }

  .desktop-facet-sidebar {
    display: none;
  }

  .search-results-layout .search-results-container {
    background-color: white !important; /* Restore background for mobile */
    border-radius: 0 !important;
    min-height: 300px !important;
    max-width: 100% !important;
    width: 100% !important;
    margin: 0 !important;
    padding: 8px !important;
    border: none !important;
  }

  .search-results-container {
    max-width: 100% !important;
    margin: 0 !important;
    width: 100% !important;
    padding: 8px !important;
    right: 0 !important;
    left: 0 !important;
  }

  .search-results-layout .search-results-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 10px;
  }

  /* Mobile specific facet panel - slide from bottom */
  .facet-overlay {
    justify-content: center; /* Center panel */
    align-items: flex-end; /* Align to bottom */
  }

  .facet-panel {
    width: 100%; /* Full width for mobile */
    max-width: 500px; /* Max width for mobile */
    height: auto; /* Auto height for mobile */
    max-height: 80vh; /* Max height for mobile */
    border-radius: 10px 10px 0 0; /* Rounded top corners */
    animation: slideUp 0.3s ease; /* Slide up animation for mobile */
    box-shadow: none; /* Remove shadow for mobile */
  }

  /* Mobile content wrapper */
  .facet-panel .facet-content-wrapper {
    max-height: calc(80vh - 120px); /* Adjust for mobile height */
  }
}

/* Media queries untuk responsive facet */
@media (min-width: 1025px) {
  .facet-panel-desktop {
    display: block;
  }
  .facet-overlay {
    display: none !important;
  }
}

/* Responsive */
@media (max-width: 480px) {
  .cart-icon, .chat-icon {
    font-size: 20px;
  }

  .cart-icon {
    -webkit-text-stroke: 0.8px white;
  }

  .chat-icon {
    -webkit-text-stroke: 1.2px white;
    margin-left: 15px;
  }

  /* Turunkan icon cart dan chat di mobile, tapi badge tetap di posisi */
  .cart-icon, .chat-icon {
    transform: translateY(3px); /* Turunkan icon sedikit */
  }

  .cart-badge, .chat-badge {
    width: 18px !important;
    height: 18px !important;
    min-width: 18px !important;
    font-size: 10px;
    border-radius: 50% !important; /* Pastikan lingkaran sempurna */
    top: -13px !important; /* Posisi badge tetap di atas (kompensasi icon yang turun) */
    right: -6px !important; /* Posisi lebih ke kanan agar tidak terpotong */
    -webkit-text-stroke: 0.4px #ee4d2d; /* Garis lebih tipis pada layar kecil */
    line-height: 18px !important; /* Pastikan teks rata tengah vertikal */
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    box-sizing: border-box !important;
    padding: 0 !important;
    transform: translateY(-3px); /* Kompensasi agar badge tidak ikut turun */
  }

  .chat-icon .chat-dots .dot {
    width: 2px;
    height: 2px;
  }

  /* Adjust clear icon position for mobile in expanded mode */
  .search-expanded .clear-search-icon {
    right: 10px !important; /* Ensure consistent positioning on mobile */
  }
}

/* Keyword Suggestions Popup - Desktop: center, Mobile: bottom sheet */
.keyword-suggestions-popup {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  z-index: 10001;
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

/* Mobile: Bottom sheet style */
@media (max-width: 768px) {
  .keyword-suggestions-popup {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    top: auto;
    transform: none;
    background-color: white;
    padding: 20px;
    border-top-left-radius: 16px;
    border-top-right-radius: 16px;
    border-radius: 16px 16px 0 0;
    box-shadow: 0 -4px 10px rgba(0,0,0,0.1);
    max-height: 70vh;
    overflow-y: auto;
    animation: slideUp 0.3s ease;
    max-width: 100%;
    width: 100%;
    margin: 0;
  }

  @keyframes slideUp {
    from {
      transform: translateY(100%);
    }
    to {
      transform: translateY(0);
    }
  }
}

.keyword-suggestions-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.keyword-suggestions-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}

.keyword-suggestion-tag {
  background: #f5f5f5;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 14px;
  color: #333;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.keyword-suggestion-tag:hover {
  background: #ee4d2d;
  color: white;
  border-color: #ee4d2d;
}

.suggestions-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 10000;
}

/* Icon "Tidak Ditemukan" - sesuai docs/facet.html */
.not-found-icon {
  margin: 0 auto 20px;
  width: 120px;
  height: 120px;
  opacity: 0.5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.search-document-icon {
  position: relative;
  width: 100%;
  height: 100%;
}

.document-base {
  position: absolute;
  width: 70px;
  height: 85px;
  background-color: #ddd;
  border-radius: 5px;
  top: 20px;
  left: 25px;
}

.document-fold {
  position: absolute;
  width: 20px;
  height: 20px;
  background-color: #eee;
  top: 20px;
  right: 25px;
  clip-path: polygon(0 0, 100% 100%, 100% 0);
}

.document-lines {
  position: absolute;
  width: 40px;
  height: 4px;
  background-color: #ccc;
  top: 45px;
  left: 40px;
}

.document-lines:before {
  content: '';
  position: absolute;
  width: 30px;
  height: 4px;
  background-color: #ccc;
  top: 12px;
  left: 0;
}

.document-lines:after {
  content: '';
  position: absolute;
  width: 20px;
  height: 4px;
  background-color: #ccc;
  top: 24px;
  left: 0;
}

.magnifying-glass {
  position: absolute;
  width: 40px;
  height: 40px;
  border: 6px solid #ccc;
  border-radius: 50%;
  top: 10px;
  right: 10px;
}

.magnifying-glass:after {
  content: '';
  position: absolute;
  width: 6px;
  height: 20px;
  background-color: #ccc;
  transform: rotate(-45deg);
  bottom: -15px;
  right: -5px;
  border-radius: 10px;
}

/* Mobile responsive untuk popup */
@media (max-width: 480px) {
  .keyword-suggestions-popup {
    max-width: 100%;
    padding: 16px;
    border-radius: 16px 16px 0 0;
  }

  .keyword-suggestion-tag {
    font-size: 13px;
    padding: 6px 12px;
  }

  .keyword-suggestions-title {
    font-size: 15px;
    margin-bottom: 12px;
  }

  .not-found-icon {
    width: 100px;
    height: 100px;
  }
}

/* Layout "Tidak Ditemukan" Full Width - Desktop */
.not-found-full-layout {
  width: 100%;
  max-width: 100vw;
  margin: 0;
  padding: 0;
  background-color: #fff;
  min-height: calc(100vh - 60px); /* Full height minus header */
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  top: 60px; /* Offset for header */
}

.not-found-container {
  text-align: center;
  padding: 30px 20px 40px;
  margin: 0;
  background-color: #fff;
  width: 100%;
  max-width: 600px;
  position: relative;
}

.not-found-title {
  font-size: 18px;
  font-weight: 500;
  color: #333;
  margin-bottom: 10px;
}

.not-found-message {
  font-size: 14px;
  color: #888;
  margin-bottom: 25px;
}

.not-found-button-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
  margin: 0 auto;
}

.not-found-button {
  border-radius: 4px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  border: none;
  transition: all 0.2s ease;
}

.not-found-button.primary {
  background-color: #ee4d2d;
  color: white;
}

.not-found-button.primary:hover {
  background-color: #e04224;
}

.not-found-button.secondary {
  background-color: white;
  color: #ee4d2d;
  border: 1px solid #ee4d2d;
}

.not-found-button.secondary:hover {
  background-color: rgba(238, 77, 45, 0.05);
}

/* Mobile responsive untuk not found layout */
@media (max-width: 768px) {
  .not-found-full-layout {
    min-height: calc(100vh - 60px);
    top: 60px;
  }

  .not-found-container {
    padding: 20px 15px 30px;
    margin: 10px 0;
  }

  .not-found-button-container {
    max-width: 100%;
  }

  .not-found-message {
    max-width: 95%;
    margin: 0 auto 25px;
  }

  .not-found-button {
    padding: 10px 12px;
    font-size: 13px;
  }
}

/* ===== SELLZIO CATEGORIES STYLES ===== */

/* Categories Section */
.sellzio-categories-section {
  background-color: #ffffff; /* Kembalikan ke warna putih seperti semula */
  padding: 16px 0;
  border: 1px solid #e5e7eb; /* border-gray-200 */
  border-radius: 0.5rem; /* rounded-lg */
  margin: 0 auto; /* Auto margin untuk center, no top margin */
  max-width: 1200px; /* Sama dengan container lainnya */
  width: calc(100% - 32px); /* Lebar penuh minus padding samping */
  position: relative;
  z-index: 10;
  box-shadow: none;
}

/* Styling untuk memastikan ikon sejajar dan 2 baris untuk desktop/tablet */
.desktop-categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  grid-template-rows: repeat(2, auto); /* 2 baris untuk desktop/tablet */
  grid-auto-flow: column; /* Susunan atas-bawah dulu (vertikal) bukan ke samping */
  gap: 8px;
  padding: 0 8px;
  min-height: 200px; /* Ubah ke min-height dan tambah tinggi untuk 2 baris */
  overflow: visible; /* Ubah ke visible agar tidak terpotong */
}

/* Styling untuk setiap item kategori - persegi untuk desktop/tablet */
.desktop-categories-grid > div {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: flex-start; /* Alignment dari atas untuk konsistensi */
  width: 95px; /* Tambah lebar untuk lebih banyak ruang text */
  height: 95px; /* Tinggi sama dengan lebar = persegi */
  min-height: 95px;
  box-shadow: none; /* Menghapus shadow dari item kategori */
  background-color: #f9fafb; /* Kembalikan ke warna semula sama dengan background halaman */
  border: 1px solid #e5e7eb; /* Menambahkan border tipis berwarna abu-abu muda */
  border-radius: 0.5rem; /* Memberikan sudut yang lembut */
  transition: border-color 0.2s ease; /* Efek transisi saat hover */
  padding: 8px 4px; /* Padding untuk spacing yang konsisten */
}

/* Efek hover pada card kategori */
.desktop-categories-grid > div:hover {
  border-color: #d1d5db; /* Warna border sedikit lebih gelap saat dihover */
}

/* Styling untuk container ikon - proporsional dengan card yang lebih besar */
.desktop-categories-grid > div > div:first-child {
  height: 45px; /* Sesuaikan dengan card yang lebih besar */
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 6px;
  margin-top: 4px; /* Margin top untuk spacing dari atas card */
  flex-shrink: 0; /* Pastikan container icon tidak mengecil */
}

/* Styling untuk ikon - ukuran proporsional */
.desktop-categories-grid > div > div:first-child > div {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 45px; /* Sesuaikan dengan card yang lebih besar */
  height: 45px;
  min-width: 45px;
  min-height: 45px;
  font-size: 24px; /* Ukuran font yang proporsional */
  border-radius: 50%;
  flex-shrink: 0; /* Pastikan icon tidak mengecil */
}

/* Styling untuk nama kategori - posisi fleksibel di bawah */
.desktop-categories-grid > div > div:last-child {
  flex: 1; /* Ambil sisa ruang yang tersedia */
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  min-height: 36px; /* Lebih banyak ruang untuk text dengan card yang lebih besar */
  padding: 0 3px; /* Tambah padding horizontal untuk text */
  font-size: 11px; /* Tambah ukuran font text */
  line-height: 1.2; /* Line height untuk readability */
  overflow: hidden; /* Sembunyikan text yang overflow */
  word-wrap: break-word; /* Break word jika terlalu panjang */
}

/* Mobile responsive untuk categories section */
@media (max-width: 768px) {
  .sellzio-categories-section {
    margin: 0 auto; /* Auto margin untuk center di mobile, no top margin */
    padding: 20px 0; /* Lebih banyak padding untuk mobile */
    width: calc(100% - 16px); /* Sesuaikan lebar untuk mobile */
  }

  /* Tetap batasi 2 baris di mobile untuk menghemat ruang */
  .desktop-categories-grid {
    grid-template-rows: repeat(2, auto); /* Kembalikan pembatasan 2 baris untuk mobile */
    max-height: 180px; /* Kembalikan max-height untuk mobile */
    overflow: hidden; /* Kembalikan overflow hidden untuk mobile */
  }
}

/* iPad Mini specific (768px x 1024px) */
@media (min-width: 768px) and (max-width: 820px) and (orientation: portrait) {
  .sellzio-categories-section {
    margin: 0 auto; /* Auto margin untuk center di iPad Mini, no top margin */
    padding: 18px 0;
    display: block !important; /* Pastikan terlihat */
    visibility: visible !important;
    width: calc(100% - 24px); /* Sesuaikan lebar untuk iPad Mini */
  }

  /* Untuk iPad Mini, gunakan 2 baris dengan susunan vertikal */
  .desktop-categories-grid {
    grid-template-rows: repeat(2, auto);
    grid-auto-flow: column; /* Susunan vertikal */
    min-height: 200px; /* Ubah ke min-height */
    overflow: visible; /* Tidak terpotong */
  }

  /* Card persegi untuk iPad Mini */
  .desktop-categories-grid > div {
    width: 100px; /* Tambah lebar untuk iPad Mini */
    height: 100px; /* Tinggi sama dengan lebar */
    min-height: 100px;
  }

  /* Icon dan text untuk iPad Mini */
  .desktop-categories-grid > div > div:first-child {
    height: 48px; /* Tambah tinggi icon container */
  }

  .desktop-categories-grid > div > div:first-child > div {
    width: 48px; /* Tambah ukuran icon */
    height: 48px;
    min-width: 48px;
    min-height: 48px;
    font-size: 25px; /* Tambah ukuran font icon */
  }

  .desktop-categories-grid > div > div:last-child {
    font-size: 11px; /* Tambah ukuran font text */
    min-height: 40px; /* Tambah tinggi area text */
    line-height: 1.3;
  }
}

/* Category Item Styles */
.category-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 8px;
  background-color: white;
  border: 1px solid #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 80px;
  height: 80px;
  position: relative;
  z-index: 1;
  pointer-events: auto;
}

.category-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 2;
}

.category-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  line-height: 1;
}

.category-name {
  font-size: 12px;
  text-align: center;
  color: #333;
  line-height: 1.2;
}

/* Desktop Categories Grid */
.desktop-categories-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  grid-template-rows: repeat(2, auto); /* 2 baris untuk desktop */
  grid-auto-flow: column; /* Susunan vertikal */
  gap: 12px;
  padding: 16px;
  width: 100%;
  max-width: 1200px; /* Sama dengan container lainnya */
  margin: 0 auto;
  justify-items: center;
  min-height: 200px; /* Tinggi minimum untuk 2 baris card persegi */
  overflow: visible; /* Tidak terpotong */
}

/* Large Desktop */
@media (min-width: 1200px) {
  .desktop-categories-grid {
    grid-template-columns: repeat(8, 1fr);
    grid-template-rows: repeat(2, auto); /* 2 baris */
    grid-auto-flow: column; /* Susunan vertikal */
    max-width: 1200px; /* Sama dengan container lainnya */
    min-height: 220px; /* Tinggi lebih besar untuk large desktop */
    overflow: visible;
  }

  /* Card persegi lebih besar untuk large desktop */
  .desktop-categories-grid > div {
    width: 105px; /* Tambah lebar untuk large desktop */
    height: 105px; /* Tinggi sama dengan lebar */
    min-height: 105px;
  }

  /* Icon dan text lebih besar untuk large desktop */
  .desktop-categories-grid > div > div:first-child {
    height: 50px; /* Tambah tinggi icon container */
  }

  .desktop-categories-grid > div > div:first-child > div {
    width: 50px; /* Tambah ukuran icon */
    height: 50px;
    min-width: 50px;
    min-height: 50px;
    font-size: 26px; /* Tambah ukuran font icon */
  }

  .desktop-categories-grid > div > div:last-child {
    font-size: 12px; /* Tambah ukuran font text */
    min-height: 42px; /* Tambah tinggi area text */
    line-height: 1.3;
  }
}

/* Desktop responsive */
@media (max-width: 1199px) and (min-width: 1025px) {
  .desktop-categories-grid {
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: repeat(2, auto); /* 2 baris */
    grid-auto-flow: column; /* Susunan vertikal */
    max-width: 1000px; /* Lebih lebar untuk konsistensi */
    min-height: 200px;
    overflow: visible;
  }
}

/* Tablet responsive */
@media (max-width: 1024px) and (min-width: 769px) {
  .desktop-categories-grid {
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(2, auto); /* 2 baris untuk tablet */
    grid-auto-flow: column; /* Susunan vertikal */
    max-width: 800px; /* Lebih lebar untuk tablet */
    min-height: 200px;
    overflow: visible;
  }

  /* Card persegi untuk tablet */
  .desktop-categories-grid > div {
    width: 100px; /* Tambah lebar untuk tablet */
    height: 100px; /* Tinggi sama dengan lebar */
    min-height: 100px;
  }

  /* Icon dan text untuk tablet */
  .desktop-categories-grid > div > div:first-child {
    height: 48px; /* Tambah tinggi icon container */
  }

  .desktop-categories-grid > div > div:first-child > div {
    width: 48px; /* Tambah ukuran icon */
    height: 48px;
    min-width: 48px;
    min-height: 48px;
    font-size: 25px; /* Tambah ukuran font icon */
  }

  .desktop-categories-grid > div > div:last-child {
    font-size: 11px; /* Tambah ukuran font text */
    min-height: 40px; /* Tambah tinggi area text */
    line-height: 1.3;
  }
}

/* Mobile Categories Container */
.categories-container-wrapper {
  position: relative;
  padding: 0 16px;
  margin-top: 0; /* Reset margin untuk mobile */
}

/* Mobile specific adjustments */
@media (max-width: 768px) {
  .categories-container-wrapper {
    padding: 0 16px;
    margin-top: 0;
    position: relative;
    z-index: 10;
  }
}

/* Gradient untuk efek fade di sisi kanan */
.categories-container-wrapper::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50px;
  height: 100%;
  background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,0.9));
  pointer-events: none;
}

.categories-container {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.categories-container::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Desktop Subcategory Overlay */
.desktop-subcategory-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-start; /* Ubah dari center ke flex-start */
  justify-content: center;
  padding-top: 80px; /* Tambahkan padding top untuk offset header */
}

/* Desktop Subcategory Expanded View - Inline tanpa overlay */
.desktop-subcategory-expanded-view {
  width: 100%;
  background-color: white;
  border-radius: 8px;
  margin-top: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
}

/* Desktop Subcategory Inline View - Benar-benar inline tanpa popup */
.desktop-subcategory-inline-view {
  width: 100%;
  margin-top: 20px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
  overflow: hidden;
  background-color: white;
}

/* Desktop Subcategory Full Height View - Tanpa header, full ke atas */
.desktop-subcategory-inline-view.h-full {
  height: 100vh;
  margin-top: 0;
  border-radius: 0;
}

/* Hide header when in expanded mode */
body.hide-header .header {
  display: none !important;
}

body.hide-header .search-expanded {
  display: none !important;
}

/* Hide scrollbar untuk sidebar dan content area */
.hide-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* WebKit browsers */
}

.subcategory-container {
  background-color: white;
  border-radius: 8px;
  max-width: 800px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  /* Hilangkan scrollbar di container utama */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.subcategory-container::-webkit-scrollbar {
  display: none; /* WebKit browsers */
}

.category-sidebar {
  width: 200px;
  background-color: #f8f9fa;
  border-right: 1px solid #e0e0e0;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
  position: sticky; /* Membuat sidebar sticky */
  top: 0; /* Posisi dari atas */
  align-self: flex-start; /* Agar tidak mengikuti tinggi container */
  z-index: 50; /* Z-index sedang untuk sidebar */
}

.category-sidebar::-webkit-scrollbar {
  display: none; /* WebKit */
}

.sidebar-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border-bottom: 1px solid #e8e9ea;
}

.sidebar-item:hover {
  background-color: #e9ecef;
}

.sidebar-item.active {
  background-color: #fff5f5;
  border-right: 3px solid #ee4d2d;
}

.sidebar-icon {
  margin-right: 12px;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  line-height: 1;
}

.sidebar-text {
  font-size: 14px;
  color: #333;
  font-weight: 500;
}

.sidebar-sticky-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: #f8f9fa;
  border-bottom: 1px solid #e0e0e0;
  padding: 12px 16px;
  margin-bottom: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.sidebar-categories {
  overflow-y: auto;
  flex: 1;
  /* Hilangkan scrollbar tapi tetap bisa scroll */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.sidebar-categories::-webkit-scrollbar {
  display: none; /* WebKit browsers */
}

.subcategory-content {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  /* Hilangkan scrollbar di area konten subcategory */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.subcategory-content::-webkit-scrollbar {
  display: none; /* WebKit browsers */
}

.subcategory-header {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 0; /* Hilangkan margin bottom */
  padding: 20px 20px 24px 20px; /* Tingkatkan padding atas dan bawah */
  border-bottom: 1px solid #e0e0e0;
  position: sticky; /* Membuat header sticky */
  top: 0; /* Posisi dari atas */
  background-color: white; /* Background putih agar tidak transparan */
  z-index: 100; /* Z-index tinggi agar di atas semua konten */
  box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* Tambah shadow untuk efek depth */
  backdrop-filter: blur(10px); /* Tambah blur effect untuk background */
  -webkit-backdrop-filter: blur(10px); /* Safari support */
  min-height: 70px; /* Pastikan tinggi minimum */
}

.subcategory-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.back-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  margin-right: 16px;
  padding: 4px 8px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.back-button:hover {
  background-color: #f0f0f0;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s;
  width: 36px;
  height: 36px;
}

.close-button:hover {
  background-color: #f0f0f0;
  color: #333;
}

.close-button-top-right {
  position: absolute;
  top: 16px;
  right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s;
  width: 36px;
  height: 36px;
  z-index: 20;
}

.close-button-top-right:hover {
  background-color: #f0f0f0;
  color: #333;
}

.subcategory-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.subcategory-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
}

.subcategory-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px 8px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid #e0e0e0;
  background-color: white;
}

.subcategory-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-color: #ee4d2d;
}

.subcategory-icon {
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 8px;
  border-radius: 50%;
  background-color: rgba(238, 77, 45, 0.1);
  font-size: 24px;
  line-height: 1;
}

.subcategory-item .subcategory-title {
  font-size: 12px;
  text-align: center;
  color: #333;
  line-height: 1.3;
  font-weight: 500;
}

/* CSS untuk Sellzio Live dan Video */
.container1 {
  display: flex;
  justify-content: space-between;
  width: calc(100% - 32px); /* Sama dengan container kategori */
  padding: 10px;
  gap: 8px; /* Gap antara card */
  max-width: 1200px; /* Sama dengan container lainnya */
  margin: 0 auto; /* Tengahkan container dengan auto margin */
  background-color: transparent; /* Transparan agar sesuai dengan background halaman */
}

/* Spacing untuk Sellzio Live & Video - sama dengan jarak kategori */
.sellzio-live-video-spacing {
  margin-top: 20px; /* Jarak dari container kategori */
  margin-bottom: 20px; /* Jarak ke container produk */
}

.section {
  background-color: white;
  border-radius: 5px;
  padding: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  width: 50%;
  cursor: pointer;
  transition: transform 0.2s;
  border: 1px solid #eaeaea;
}

.section:hover {
  transform: scale(1.01);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.section-title {
  margin-left: 10px;
  font-size: 1rem;
  color: #ee4d2d;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.title-icon {
  margin-left: 5px;
  font-size: 20px;
  color: #999;
  font-weight: normal;
}

.videos-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2px;
}

.video-card {
  position: relative;
  border-radius: 6px;
  overflow: hidden;
  aspect-ratio: 9/16;
  background-color: #f8f8f8;
  cursor: pointer;
  border: 1px solid #e0e0e0; /* Tambahkan bingkai untuk video card */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05); /* Tambahkan bayangan halus */
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.video-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.video-thumbnail {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  background-position: center;
  background-size: cover;
  background-repeat: no-repeat;
}

.label {
  position: absolute;
  top: 5px;
  left: 5px;
  background-color: #ee4d2d;
  color: white;
  padding: 1px 5px;
  border-radius: 50px;
  font-size: 0.6rem;
  font-weight: bold;
  display: flex;
  align-items: center;
  z-index: 2;
}

.view-count {
  position: absolute;
  top: 5px;
  left: 5px;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  padding: 1px 5px;
  border-radius: 50px;
  font-size: 0.6rem;
  display: flex;
  align-items: center;
  z-index: 2;
}

.dot {
  height: 5px;
  width: 5px;
  background-color: white;
  border-radius: 50%;
  display: inline-block;
  margin-right: 3px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(0.8);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.2);
    opacity: 1;
  }
  100% {
    transform: scale(0.8);
    opacity: 0.7;
  }
}

.video-title {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: white;
  padding: 12px 5px 5px;
  font-size: 0.65rem;
  font-weight: bold;
}

.view-icon {
  height: 8px;
  width: 8px;
  margin-right: 3px;
  display: inline-block;
}

.play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  opacity: 0.8;
  color: white;
  background: rgba(0,0,0,0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2;
}

.play-icon i {
  font-size: 14px;
}

/* Media queries untuk responsivitas Sellzio Live & Video */
@media screen and (min-width: 768px) {
  .container1 {
    margin: 0 auto; /* Center dengan auto margin untuk tablet */
    width: calc(100% - 24px); /* Sama dengan container kategori iPad Mini */
  }

  /* Spacing untuk tablet */
  .sellzio-live-video-spacing {
    margin-top: 24px; /* Jarak dari container kategori untuk tablet */
    margin-bottom: 24px; /* Jarak ke container produk untuk tablet */
  }

  .videos-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .section {
    padding: 15px;
    border-radius: 12px;
  }

  .section-title {
    font-size: 18px;
    margin-left: 15px;
    margin-bottom: 5px;
  }

  .section-header {
    margin-bottom: 15px;
  }

  .video-title {
    font-size: 12px;
    padding: 20px 12px 10px;
  }

  .label, .view-count {
    font-size: 0.7rem;
    padding: 2px 8px;
    top: 8px;
    left: 8px;
  }

  .play-icon {
    width: 40px;
    height: 40px;
  }

  .play-icon i {
    font-size: 18px;
  }
}

@media screen and (min-width: 1024px) {
  .container1 {
    padding: 20px;
    gap: 20px;
    margin: 0 auto; /* Center dengan auto margin untuk desktop */
    width: calc(100% - 32px); /* Sama dengan container kategori desktop */
  }

  /* Spacing untuk desktop */
  .sellzio-live-video-spacing {
    margin-top: 28px; /* Jarak dari container kategori untuk desktop */
    margin-bottom: 28px; /* Jarak ke container produk untuk desktop */
  }

  .section {
    padding: 20px;
    border-radius: 16px;
  }

  .section-title {
    font-size: 20px;
    margin-left: 10px;
    margin-bottom: 8px;
  }

  .title-icon {
    font-size: 24px;
    margin-left: 8px;
  }

  .videos-grid {
    gap: 15px;
  }

  .video-card {
    max-height: 350px;
    border-radius: 10px;
    border: 2px solid #e0e0e0;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
  }

  .video-title {
    font-size: 14px;
    padding: 25px 15px 12px;
  }

  .play-icon {
    width: 50px;
    height: 50px;
  }

  .play-icon i {
    font-size: 22px;
  }

  .label, .view-count {
    font-size: 0.8rem;
    padding: 3px 10px;
    top: 12px;
    left: 12px;
    border-radius: 50px;
  }

  .dot {
    height: 6px;
    width: 6px;
  }
}

/* Mobile Responsive untuk Sellzio Live & Video */
@media (max-width: 767px) {
  .container1 {
    padding: 8px;
    gap: 6px;
    margin: 0 auto; /* Center dengan auto margin untuk mobile */
    width: calc(100% - 16px); /* Sama dengan container kategori mobile */
  }

  /* Spacing untuk mobile */
  .sellzio-live-video-spacing {
    margin-top: 16px; /* Jarak dari container kategori untuk mobile */
    margin-bottom: 16px; /* Jarak ke container produk untuk mobile */
  }

  .section {
    padding: 8px;
    border-radius: 8px;
  }

  .section-title {
    font-size: 14px;
    margin-left: 8px;
    margin-bottom: 3px;
  }

  .title-icon {
    font-size: 16px;
    margin-left: 4px;
  }

  .section-header {
    margin-bottom: 8px;
  }

  .videos-grid {
    gap: 4px;
  }

  .video-title {
    font-size: 10px;
    padding: 8px 4px 4px;
  }

  .label, .view-count {
    font-size: 0.5rem;
    padding: 1px 4px;
    top: 4px;
    left: 4px;
  }

  .play-icon {
    width: 25px;
    height: 25px;
  }

  .play-icon i {
    font-size: 12px;
  }

  .dot {
    height: 4px;
    width: 4px;
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .desktop-categories-grid {
    display: none;
  }

  .subcategory-container {
    width: 95%;
    max-height: 90vh;
    flex-direction: column;
  }

  .category-sidebar {
    width: 100%;
    max-height: 200px;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }

  .subcategory-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 12px;
  }

  .subcategory-item {
    padding: 12px 6px;
  }

  .subcategory-icon {
    width: 32px;
    height: 32px;
  }
}

/* Isolasi SubcategoryView dari styling produk card */
.subcategory-view-container {
  /* Reset semua styling yang mungkin konflik dengan produk card */
  position: fixed;
  inset: 0;
  background: white;
  z-index: 50;
  overflow-y: auto;
  /* Isolasi komponen dari global styles */
  isolation: isolate;
}

.subcategory-view-container * {
  /* Pastikan tidak ada styling produk card yang bocor */
  box-sizing: border-box;
}

/* Reset dan blokir semua pseudo-elements yang mungkin dari produk card */
.subcategory-view-container *::before,
.subcategory-view-container *::after {
  content: none !important;
  display: none !important;
}

/* Blokir semua animasi dan transform yang mungkin dari produk card */
.subcategory-view-container * {
  animation: none !important;
  transition: all 0.2s ease !important;
}

/* Pastikan badge, icon play, dan komponen produk card tidak muncul di subcategory */
.subcategory-view-container .sellzio-badge,
.subcategory-view-container .sellzio-discount-badge,
.subcategory-view-container .sellzio-cod-badge,
.subcategory-view-container .sellzio-mall-badge,
.subcategory-view-container .sellzio-star-badge,
.subcategory-view-container .sellzio-rating-stars,
.subcategory-view-container .sellzio-shipping-badge,
.subcategory-view-container .play-icon,
.subcategory-view-container .video-overlay,
.subcategory-view-container .image-slider-dots,
.subcategory-view-container .flash-sale-timer,
.subcategory-view-container .product-card-overlay,
.subcategory-view-container .discount-ticket,
.subcategory-view-container .cod-badge,
.subcategory-view-container .mall-badge,
.subcategory-view-container .star-badge,
.subcategory-view-container .rating-stars,
.subcategory-view-container .shipping-badge,
.subcategory-view-container .live-badge,
.subcategory-view-container .video-play-button,
.subcategory-view-container .image-slider-indicator,
.subcategory-view-container .flash-sale-progress,
.subcategory-view-container .product-overlay,
.subcategory-view-container .badge,
.subcategory-view-container .overlay,
.subcategory-view-container .slider,
.subcategory-view-container .timer {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
}

/* Pastikan subcategory item tidak terpengaruh styling produk card */
.subcategory-view-container .subcategory-item-clean {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 12px 8px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  cursor: pointer;
  min-height: 80px;
}

.subcategory-view-container .subcategory-item-clean:hover {
  border-color: #ee4d2d;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.subcategory-view-container .subcategory-icon-clean {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin-bottom: 8px;
  background-color: rgba(238, 77, 45, 0.1);
}

.subcategory-view-container .subcategory-title-clean {
  font-size: 12px;
  text-align: center;
  color: #333;
  line-height: 1.3;
  font-weight: 500;
  word-wrap: break-word;
}

/* Blokir semua elemen yang mengandung class produk card */
.subcategory-view-container [class*="product-"],
.subcategory-view-container [class*="card-"],
.subcategory-view-container [class*="badge-"],
.subcategory-view-container [class*="discount-"],
.subcategory-view-container [class*="cod-"],
.subcategory-view-container [class*="mall-"],
.subcategory-view-container [class*="star-"],
.subcategory-view-container [class*="rating-"],
.subcategory-view-container [class*="shipping-"],
.subcategory-view-container [class*="live-"],
.subcategory-view-container [class*="video-"],
.subcategory-view-container [class*="play-"],
.subcategory-view-container [class*="slider-"],
.subcategory-view-container [class*="flash-"],
.subcategory-view-container [class*="timer-"],
.subcategory-view-container [class*="overlay-"] {
  /* Hanya blokir jika bukan subcategory item */
}

.subcategory-view-container [class*="product-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="card-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="badge-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="discount-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="cod-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="mall-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="star-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="rating-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="shipping-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="live-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="video-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="play-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="slider-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="flash-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="timer-"]:not([class*="subcategory"]),
.subcategory-view-container [class*="overlay-"]:not([class*="subcategory"]) {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* Blokir semua elemen dengan tag tertentu yang mungkin dari produk card */
.subcategory-view-container img:not(.subcategory-icon-clean *),
.subcategory-view-container svg:not(.subcategory-icon-clean *),
.subcategory-view-container button:not(.subcategory-view-container > * button),
.subcategory-view-container span:not(.subcategory-title-clean):not(.subcategory-icon-clean *),
.subcategory-view-container div:not(.subcategory-item-clean):not(.subcategory-icon-clean):not(.subcategory-view-container > *) {
  /* Hanya tampilkan elemen yang memang bagian dari subcategory */
}

/* Whitelist hanya elemen subcategory yang diizinkan */
.subcategory-view-container > *,
.subcategory-view-container .subcategory-item-clean,
.subcategory-view-container .subcategory-icon-clean,
.subcategory-view-container .subcategory-title-clean,
.subcategory-view-container .sticky,
.subcategory-view-container .flex,
.subcategory-view-container .grid,
.subcategory-view-container .p-4,
.subcategory-view-container .bg-white,
.subcategory-view-container .border-b,
.subcategory-view-container .border-gray-200,
.subcategory-view-container .px-4,
.subcategory-view-container .py-3,
.subcategory-view-container .z-10,
.subcategory-view-container .items-center,
.subcategory-view-container .text-gray-600,
.subcategory-view-container .hover\\:text-gray-800,
.subcategory-view-container .transition-colors,
.subcategory-view-container .mr-3,
.subcategory-view-container .flex-1,
.subcategory-view-container .w-12,
.subcategory-view-container .h-12,
.subcategory-view-container .rounded-full,
.subcategory-view-container .justify-center,
.subcategory-view-container .text-2xl,
.subcategory-view-container .text-lg,
.subcategory-view-container .font-semibold,
.subcategory-view-container .text-gray-800,
.subcategory-view-container .ml-2,
.subcategory-view-container .px-3,
.subcategory-view-container .py-1,
.subcategory-view-container .bg-gray-100,
.subcategory-view-container .text-sm,
.subcategory-view-container .hover\\:bg-gray-200,
.subcategory-view-container .grid-cols-4,
.subcategory-view-container .gap-3 {
  /* Elemen yang diizinkan */
  display: inherit !important;
  visibility: inherit !important;
  opacity: inherit !important;
}

/* PERBAIKAN: Badge diskon flash sale menempel di foto produk dengan z-index yang tepat */
.sellzio-image-container {
  position: relative !important;
  z-index: 1 !important;
}

.sellzio-image-container .sellzio-product-image {
  z-index: 2 !important;
  position: relative !important;
}

.sellzio-image-container > div[class*="absolute"] {
  z-index: 3 !important;
  position: absolute !important;
}

/* PERBAIKAN: Pastikan badge diskon flash sale menempel di container gambar */
.sellzio-flash-discount-badge-right {
  position: absolute !important;
  top: 8px !important;
  right: 8px !important;
  z-index: 30 !important; /* Z-index lebih tinggi untuk flash sale */
  pointer-events: none !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* PERBAIKAN: Badge diskon flash sale harus terlihat dengan display flex */
.sellzio-flash-discount-badge-right > div {
  display: flex !important;
  position: relative !important;
  visibility: visible !important;
  opacity: 1 !important;
  background-color: #ff6b35 !important;
  color: white !important;
  border-radius: 3px !important;
  overflow: hidden !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  z-index: 30 !important; /* Z-index tinggi untuk flash sale badge */
  min-height: 14px !important; /* Pastikan badge memiliki tinggi minimum */
  font-size: 10px !important;
  font-weight: bold !important;
}

/* PERBAIKAN: Badge diskon harus berada di atas foto produk flash sale */
.sellzio-flash-sale-card .sellzio-image-container > div {
  z-index: 30 !important; /* Z-index tinggi untuk flash sale */
}

/* PERBAIKAN: Flash sale card image container */
.sellzio-flash-sale-card .sellzio-image-container {
  position: relative !important;
  z-index: 1 !important;
}

.sellzio-flash-sale-card .sellzio-product-image {
  z-index: 2 !important;
}

.sellzio-flash-sale-card .sellzio-image-placeholder {
  z-index: 1 !important;
}

/* PERBAIKAN: Pastikan Next.js Image component tidak mengoverride z-index di flash sale */
.sellzio-image-container img {
  z-index: 2 !important;
}

.sellzio-image-container > div:first-child {
  z-index: 2 !important;
}

/* PERBAIKAN: Badge diskon corner di pojok kanan atas foto produk */
.sellzio-product-image-container > div[class*="bg-[#ff6b35]"] {
  position: absolute !important;
  top: 8px !important; /* Sedikit ke bawah dari tepi atas */
  right: 8px !important; /* Di pojok kanan atas */
  z-index: 3 !important;
  pointer-events: none !important;
}

/* PERBAIKAN: Video card z-index hierarchy yang benar */
.sellzio-video-container {
  position: relative !important;
  z-index: 1 !important;
}

.sellzio-video-container .sellzio-video-thumbnail {
  z-index: 2 !important;
  position: relative !important;
}

.sellzio-video-container .sellzio-video-placeholder {
  z-index: 1 !important;
}

.sellzio-video-container .sellzio-play-button {
  z-index: 20 !important;
  position: absolute !important;
}

/* PERBAIKAN: Badge LIVE di video harus di bawah tombol play */
.sellzio-video-container > div[class*="absolute"][class*="bg-[#ee4d2d]"] {
  z-index: 15 !important;
  position: absolute !important;
}

/* PERBAIKAN: Iframe video */
.sellzio-video-container iframe {
  z-index: 10 !important;
}

/* PERBAIKAN: Image slider card z-index hierarchy */
.sellzio-image-slider-card .sellzio-image-container {
  position: relative !important;
  z-index: 1 !important;
}

.sellzio-image-slider-card .sellzio-image-slide {
  z-index: 2 !important;
}

.sellzio-image-slider-card .sellzio-image-placeholder {
  z-index: 1 !important;
}

.sellzio-image-slider-card .sellzio-image-dots {
  z-index: 20 !important;
}

/* PERBAIKAN: Badge diskon di image slider harus di atas semua elemen */
.sellzio-image-slider-card .sellzio-image-container > div[class*="bg-[#ff6b35]"] {
  position: absolute !important;
  top: 8px !important;
  right: 8px !important;
  z-index: 25 !important;
  pointer-events: none !important;
  display: inline-flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}
