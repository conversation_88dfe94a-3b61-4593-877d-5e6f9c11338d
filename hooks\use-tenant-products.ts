"use client"

import { useState, useEffect } from 'react'
import { getClient } from '@/lib/supabase'

interface Product {
  id: number
  name: string
  shortName?: string
  category: string
  subcategory?: string
  price: string
  originalPrice?: string
  discount?: string
  rating?: string
  sold?: string
  shipping?: string
  image: string
  isMall?: boolean
  cod?: boolean
  isTerlaris?: boolean
  isLive?: boolean
  address?: {
    province: string
    city: string
    district: string
    village: string
  }
  searchScore?: number
  matchDetails?: string[]
}

interface UseTenantProductsReturn {
  products: Product[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useTenantProducts(tenantSlug: string): UseTenantProductsReturn {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchProducts = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('🔥 HOOK: Starting to fetch products for tenant:', tenantSlug)

      const supabase = getClient()

      // Fetch products from database filtered by tenant
      const { data, error: fetchError } = await supabase
        .from('products')
        .select(`
          id,
          sku,
          name,
          slug,
          description,
          short_description,
          price,
          compare_price,
          featured_image,
          featured,
          status,
          visibility,
          created_at,
          updated_at,
          tenant_id
        `)
        .eq('status', 'active')
        .eq('visibility', 'visible')
        .eq('tenant_id', tenantSlug)
        .order('created_at', { ascending: false })

      if (fetchError) {
        throw fetchError
      }

      console.log('🔥 TENANT PRODUCTS: Fetched from database:', data?.length || 0, 'products for tenant:', tenantSlug)

      // Transform database data to match our Product interface
      const transformedProducts: Product[] = (data || []).map((item: any, index: number) => {
        // Calculate discount percentage if compare_price exists
        const discountPercentage = item.compare_price && item.price
          ? Math.round(((item.compare_price - item.price) / item.compare_price) * 100)
          : 0

        return {
          id: index + 1, // Use index + 1 for simple numeric ID
          name: item.name || 'Unnamed Product',
          shortName: item.short_description || item.name?.substring(0, 50) || 'Unnamed Product',
          category: 'Elektronik', // Default category since we don't have category table relation yet
          subcategory: undefined,
          price: formatPrice(item.price || 0),
          originalPrice: item.compare_price ? formatPrice(item.compare_price) : undefined,
          discount: discountPercentage > 0 ? `${discountPercentage}%` : undefined,
          rating: (Math.random() * 2 + 3).toFixed(1), // Random rating between 3.0-5.0
          sold: `${Math.floor(Math.random() * 500)} terjual`, // Random sold count
          shipping: 'Gratis Ongkir',
          image: item.featured_image || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=300&fit=crop',
          isMall: Math.random() > 0.5, // Random mall status
          cod: Math.random() > 0.3, // Random COD availability
          isTerlaris: item.featured || false,
          isLive: Math.random() > 0.8, // Random live status
          address: {
            province: ['DKI Jakarta', 'Jawa Barat', 'Jawa Timur', 'Jawa Tengah', 'DI Yogyakarta', 'Banten', 'Sumatera Utara', 'Bali'][index % 8],
            city: ['Jakarta Selatan', 'Bandung', 'Surabaya', 'Semarang', 'Yogyakarta', 'Tangerang', 'Medan', 'Denpasar'][index % 8],
            district: ['Kebayoran Baru', 'Coblong', 'Gubeng', 'Banyumanik', 'Depok', 'Serpong', 'Medan Timur', 'Denpasar Selatan'][index % 8],
            village: ['Senayan', 'Dago', 'Airlangga', 'Tembalang', 'Condongcatur', 'BSD City', 'Mabar', 'Sanur'][index % 8]
          }
        }
      })

      setProducts(transformedProducts)
      console.log('🔥 TENANT PRODUCTS: Transformed products:', transformedProducts.length)
    } catch (err) {
      console.error('Error fetching products:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch products')
      
      // Fallback to sample data if database fails
      const { sampleProducts } = await import('@/components/data/products')
      setProducts(sampleProducts)
    } finally {
      setLoading(false)
    }
  }

  const refetch = async () => {
    await fetchProducts()
  }

  useEffect(() => {
    fetchProducts()
  }, [tenantSlug])

  return {
    products,
    loading,
    error,
    refetch
  }
}

// Helper function to format price
function formatPrice(price: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price).replace('IDR', 'Rp')
}
