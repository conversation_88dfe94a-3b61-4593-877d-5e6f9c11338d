"use client"

import { useState, useEffect } from 'react'
import { getClient, getServerClient } from '@/lib/supabase'

interface Product {
  id: number
  name: string
  shortName?: string
  category: string
  subcategory?: string
  price: string
  originalPrice?: string
  discount?: string
  rating?: string
  sold?: string
  shipping?: string
  image: string
  isMall?: boolean
  cod?: boolean
  isTerlaris?: boolean
  isLive?: boolean
  address?: {
    province: string
    city: string
    district: string
    village: string
  }
  searchScore?: number
  matchDetails?: string[]
}

interface UseTenantProductsReturn {
  products: Product[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useTenantProducts(tenantSlug: string): UseTenantProductsReturn {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchProducts = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('🔥 HOOK: Starting to fetch products for tenant:', tenantSlug)

      // Use regular client with RLS policies
      const supabase = getClient()
      console.log('🔥 TENANT PRODUCTS: Using Supabase client with RLS policies')

      // Test basic connection first
      const { data: testData, error: testError } = await supabase
        .from('tenants')
        .select('count')
        .limit(1)
      console.log('🔥 TENANT PRODUCTS: Connection test:', { testData, testError })

      // TEMPORARY: Hardcode tenant IDs for testing
      const tenantIdMap: { [key: string]: string } = {
        'test1': '96111d56-b82d-43e7-9926-8f0530dc6063',
        'test2': '9bb06506-e2a9-4f8d-876b-a1a10ef5f240',
        'beauty-shop': '550e8400-e29b-41d4-a716-446655440001',
        'electronic-store': '550e8400-e29b-41d4-a716-446655440002',
        'fashion-store': '550e8400-e29b-41d4-a716-446655440010'
      }

      const tenantIdToUse = tenantIdMap[tenantSlug]

      if (!tenantIdToUse) {
        console.log('🔥 TENANT PRODUCTS: Unknown tenant slug:', tenantSlug)
        const { sampleProducts } = await import('@/components/data/products')
        const tenantProducts = getTenantSpecificProducts(tenantSlug, sampleProducts)
        setProducts(tenantProducts)
        setLoading(false)
        return
      }

      console.log('🔥 TENANT PRODUCTS: Using hardcoded tenant ID:', tenantIdToUse, 'for slug:', tenantSlug)

      // Fetch products from database filtered by tenant with all needed fields
      console.log('🔥 TENANT PRODUCTS: Querying products with tenant_id:', tenantIdToUse)

      const { data, error: fetchError } = await supabase
        .from('products')
        .select(`
          id,
          sku,
          name,
          slug,
          description,
          short_description,
          price,
          compare_price,
          featured_image,
          images,
          rating,
          sold,
          shipping_info,
          address,
          cod,
          is_mall,
          flash_sale,
          discount_percentage,
          featured,
          status,
          visibility,
          created_at,
          updated_at,
          tenant_id,
          store_id
        `)
        .eq('status', 'active')
        .eq('visibility', 'visible')
        .eq('tenant_id', tenantIdToUse as string)
        .order('created_at', { ascending: false })

      console.log('🔥 TENANT PRODUCTS: Query completed. Error:', fetchError, 'Data length:', data?.length)

      if (fetchError) {
        throw fetchError
      }

      console.log('🔥 TENANT PRODUCTS: Fetched from database:', data?.length || 0, 'products for tenant:', tenantSlug)
      console.log('🔥 TENANT PRODUCTS: Raw database data:', data)

      // If we have database products, use them
      if (data && data.length > 0) {
        // Transform database data to match our Product interface
        const transformedProducts: Product[] = data.map((item: any, index: number) => {
        // Calculate discount percentage if compare_price exists
        const discountPercentage = item.compare_price && item.price
          ? Math.round(((item.compare_price - item.price) / item.compare_price) * 100)
          : 0

        // Parse JSON fields safely
        let shippingInfo = {}
        let addressInfo = {}

        try {
          shippingInfo = typeof item.shipping_info === 'string' ? JSON.parse(item.shipping_info) : (item.shipping_info || {})
          addressInfo = typeof item.address === 'string' ? JSON.parse(item.address) : (item.address || {})
        } catch (e) {
          console.log('🔥 JSON PARSE ERROR for product:', item.name, e)
          shippingInfo = {}
          addressInfo = {}
        }

          return {
            id: index + 1, // Use index + 1 for simple numeric ID
            name: item.name || 'Unnamed Product',
            shortName: item.short_description || item.name?.substring(0, 50) || 'Unnamed Product',
            category: 'Elektronik', // Default category since we don't have category table relation yet
            subcategory: undefined,
            price: formatPrice(item.price || 0),
            originalPrice: item.compare_price ? formatPrice(item.compare_price) : undefined,
            discount: item.discount_percentage > 0 ? `${item.discount_percentage}%` : (discountPercentage > 0 ? `${discountPercentage}%` : undefined),
            rating: item.rating ? item.rating.toString() : '0',
            sold: item.sold ? `${item.sold}+` : '0+',
            shipping: (shippingInfo as any).type || 'Gratis Ongkir',
            image: item.featured_image || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=300&fit=crop',
            isMall: item.is_mall || false,
            cod: item.cod || false,
            isTerlaris: item.sold > 100,
            isLive: item.flash_sale || false,
            address: {
              province: (addressInfo as any).province || 'DKI Jakarta',
              city: (addressInfo as any).city || 'Jakarta',
              district: (addressInfo as any).district || 'Senayan',
              village: (addressInfo as any).village || 'Senayan'
            }
          }
        })

        setProducts(transformedProducts)
        console.log('🔥 TENANT PRODUCTS: Transformed products:', transformedProducts.length)
        console.log('🔥 TENANT PRODUCTS: Final products data:', transformedProducts)
      } else {
        // Only fallback to sample data if database is truly empty
        console.log('🔥 TENANT PRODUCTS: No database products found, using tenant-specific sample data')
        const { sampleProducts } = await import('@/components/data/products')
        const tenantProducts = getTenantSpecificProducts(tenantSlug, sampleProducts)
        setProducts(tenantProducts)
      }
    } catch (err) {
      console.error('Error fetching products:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch products')
      
      // Fallback to tenant-specific sample data if database fails
      const { sampleProducts } = await import('@/components/data/products')
      const tenantProducts = getTenantSpecificProducts(tenantSlug, sampleProducts)
      setProducts(tenantProducts)
    } finally {
      setLoading(false)
    }
  }

  const refetch = async () => {
    await fetchProducts()
  }

  useEffect(() => {
    fetchProducts()
  }, [tenantSlug])

  return {
    products,
    loading,
    error,
    refetch
  }
}

// Helper function to format price
function formatPrice(price: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price).replace('IDR', 'Rp')
}

// Helper function to get tenant-specific products
function getTenantSpecificProducts(tenantSlug: string, sampleProducts: any[]): Product[] {
  console.log('🔥 TENANT PRODUCTS: Creating tenant-specific products for:', tenantSlug)

  // Define tenant-specific product configurations
  const tenantConfigs: { [key: string]: {
    categories: string[],
    cityOverride: string,
    provinceOverride: string,
    productCount: number,
    startIndex: number
  } } = {
    'test1': {
      categories: ['Elektronik'],
      cityOverride: 'Jakarta',
      provinceOverride: 'DKI Jakarta',
      productCount: 20,
      startIndex: 0
    },
    'test2': {
      categories: ['Elektronik'],
      cityOverride: 'Bandung',
      provinceOverride: 'Jawa Barat',
      productCount: 15,
      startIndex: 5
    },
    'beauty-shop': {
      categories: ['Kecantikan', 'Fashion'],
      cityOverride: 'Surabaya',
      provinceOverride: 'Jawa Timur',
      productCount: 18,
      startIndex: 10
    },
    'electronic-store': {
      categories: ['Elektronik'],
      cityOverride: 'Medan',
      provinceOverride: 'Sumatera Utara',
      productCount: 25,
      startIndex: 15
    },
    'fashion-store': {
      categories: ['Fashion'],
      cityOverride: 'Yogyakarta',
      provinceOverride: 'DI Yogyakarta',
      productCount: 12,
      startIndex: 20
    }
  }

  const config = tenantConfigs[tenantSlug] || tenantConfigs['test1']

  // Get products for this tenant (with rotation to avoid same products)
  const tenantProducts = sampleProducts
    .slice(config.startIndex, config.startIndex + config.productCount)
    .concat(sampleProducts.slice(0, Math.max(0, config.productCount - (sampleProducts.length - config.startIndex))))
    .slice(0, config.productCount)
    .map((product, index) => ({
      ...product,
      id: index + 1, // Reset IDs to start from 1
      address: {
        province: config.provinceOverride,
        city: config.cityOverride,
        district: 'Central District',
        village: 'Central Village'
      }
    }))

  console.log('🔥 TENANT PRODUCTS: Generated', tenantProducts.length, 'products for tenant:', tenantSlug)
  console.log('🔥 TENANT PRODUCTS: City override:', config.cityOverride)

  return tenantProducts
}
