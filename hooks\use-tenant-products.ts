"use client"

import { useState, useEffect } from 'react'
import { getClient, getServerClient } from '@/lib/supabase'

interface Product {
  id: number
  name: string
  shortName?: string
  category: string
  subcategory?: string
  price: string
  originalPrice?: string
  discount?: string
  rating?: string
  sold?: string
  shipping?: string
  image: string
  isMall?: boolean
  cod?: boolean
  isTerlaris?: boolean
  isLive?: boolean
  address?: {
    province: string
    city: string
    district: string
    village: string
  }
  searchScore?: number
  matchDetails?: string[]
}

interface UseTenantProductsReturn {
  products: Product[]
  loading: boolean
  error: string | null
  refetch: () => Promise<void>
}

export function useTenantProducts(tenantSlug: string): UseTenantProductsReturn {
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const fetchProducts = async () => {
    try {
      setLoading(true)
      setError(null)

      console.log('🔥 HOOK: Starting to fetch products for tenant:', tenantSlug)

      // Try server client first for better access
      const supabase = typeof window === 'undefined' ? getServerClient() : getClient()

      // First get tenant ID from slug
      console.log('🔥 TENANT PRODUCTS: Looking for tenant with slug:', tenantSlug)
      const { data: tenantData, error: tenantError } = await supabase
        .from('tenants')
        .select('id, name, slug')
        .eq('slug', tenantSlug)
        .single()

      console.log('🔥 TENANT PRODUCTS: Tenant query result:', { tenantData, tenantError })

      if (tenantError || !tenantData) {
        console.log('🔥 TENANT PRODUCTS: Tenant not found for slug:', tenantSlug, 'Error:', tenantError)

        // Fallback: try to get products using tenant_id directly as string (for backward compatibility)
        console.log('🔥 TENANT PRODUCTS: Trying fallback with tenant_id as string:', tenantSlug)
      } else {
        console.log('🔥 TENANT PRODUCTS: Found tenant:', tenantData.name, 'ID:', tenantData.id)
      }

      // Determine tenant ID to use for product query
      let tenantIdToUse = null
      if (tenantData && tenantData.id) {
        tenantIdToUse = tenantData.id
        console.log('🔥 TENANT PRODUCTS: Using tenant ID from database:', tenantIdToUse)
      } else {
        // Fallback: try using slug as tenant_id (for backward compatibility)
        tenantIdToUse = tenantSlug
        console.log('🔥 TENANT PRODUCTS: Using slug as tenant_id fallback:', tenantIdToUse)
      }

      // Fetch products from database filtered by tenant with all needed fields
      const { data, error: fetchError } = await supabase
        .from('products')
        .select(`
          id,
          sku,
          name,
          slug,
          description,
          short_description,
          price,
          compare_price,
          featured_image,
          images,
          rating,
          sold,
          shipping_info,
          address,
          cod,
          is_mall,
          flash_sale,
          discount_percentage,
          featured,
          status,
          visibility,
          created_at,
          updated_at,
          tenant_id,
          store_id
        `)
        .eq('status', 'active')
        .eq('visibility', 'visible')
        .eq('tenant_id', tenantIdToUse)
        .order('created_at', { ascending: false })

      if (fetchError) {
        throw fetchError
      }

      console.log('🔥 TENANT PRODUCTS: Fetched from database:', data?.length || 0, 'products for tenant:', tenantSlug)
      console.log('🔥 TENANT PRODUCTS: Raw database data:', data)

      // Transform database data to match our Product interface
      const transformedProducts: Product[] = (data || []).map((item: any, index: number) => {
        // Calculate discount percentage if compare_price exists
        const discountPercentage = item.compare_price && item.price
          ? Math.round(((item.compare_price - item.price) / item.compare_price) * 100)
          : 0

        // Parse JSON fields safely
        let shippingInfo = {}
        let addressInfo = {}

        try {
          shippingInfo = typeof item.shipping_info === 'string' ? JSON.parse(item.shipping_info) : (item.shipping_info || {})
          addressInfo = typeof item.address === 'string' ? JSON.parse(item.address) : (item.address || {})
        } catch (e) {
          console.log('🔥 JSON PARSE ERROR for product:', item.name, e)
          shippingInfo = {}
          addressInfo = {}
        }

        return {
          id: index + 1, // Use index + 1 for simple numeric ID
          name: item.name || 'Unnamed Product',
          shortName: item.short_description || item.name?.substring(0, 50) || 'Unnamed Product',
          category: 'Elektronik', // Default category since we don't have category table relation yet
          subcategory: undefined,
          price: formatPrice(item.price || 0),
          originalPrice: item.compare_price ? formatPrice(item.compare_price) : undefined,
          discount: item.discount_percentage > 0 ? `${item.discount_percentage}%` : (discountPercentage > 0 ? `${discountPercentage}%` : undefined),
          rating: item.rating ? item.rating.toString() : '0',
          sold: item.sold ? `${item.sold}+` : '0+',
          shipping: (shippingInfo as any).type || 'Gratis Ongkir',
          image: item.featured_image || 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=300&h=300&fit=crop',
          isMall: item.is_mall || false,
          cod: item.cod || false,
          isTerlaris: item.sold > 100,
          isLive: item.flash_sale || false,
          address: {
            province: (addressInfo as any).province || 'DKI Jakarta',
            city: (addressInfo as any).city || 'Jakarta',
            district: (addressInfo as any).district || 'Senayan',
            village: (addressInfo as any).village || 'Senayan'
          }
        }
      })

      setProducts(transformedProducts)
      console.log('🔥 TENANT PRODUCTS: Transformed products:', transformedProducts.length)
      console.log('🔥 TENANT PRODUCTS: Final products data:', transformedProducts)
    } catch (err) {
      console.error('Error fetching products:', err)
      setError(err instanceof Error ? err.message : 'Failed to fetch products')
      
      // Fallback to sample data if database fails
      const { sampleProducts } = await import('@/components/data/products')
      setProducts(sampleProducts)
    } finally {
      setLoading(false)
    }
  }

  const refetch = async () => {
    await fetchProducts()
  }

  useEffect(() => {
    fetchProducts()
  }, [tenantSlug])

  return {
    products,
    loading,
    error,
    refetch
  }
}

// Helper function to format price
function formatPrice(price: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(price).replace('IDR', 'Rp')
}
